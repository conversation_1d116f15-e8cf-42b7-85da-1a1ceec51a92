"""
Advanced Professional Signal Analyzer with Data-Driven Threshold Optimization
- Time-based signal generation and confirmation tracking
- PGO-style reversal detection with professional thresholds
- Advanced data analysis for threshold optimization
- Professional timeframe-adjusted signal values
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
import glob
import os
from scipy import stats
from sklearn.metrics import accuracy_score, precision_score, recall_score
import warnings
warnings.filterwarnings('ignore')

class AdvancedProfessionalSignalAnalyzer:
    def __init__(self):
        print("🚀 Advanced Professional Signal Analyzer initialized")
        print("📊 Data-driven threshold optimization enabled")
        print("⏰ Time-based signal tracking enabled")
        print("🎯 Professional reversal detection enabled")
        
        # Initialize with basic professional thresholds (will be optimized)
        self.professional_thresholds = self._initialize_professional_thresholds()
        self.optimized_thresholds = {}
        self.signal_history = {}
        self.confirmation_tracking = {}
        
    def _initialize_professional_thresholds(self) -> Dict[str, Dict[str, Dict[str, float]]]:
        """Initialize professional thresholds based on trading experience"""
        return {
            # PGO - Pretty Good Oscillator (Your example indicator)
            'PGO_14': {
                '1min': {
                    'oversold_entry': -4.5, 'oversold_signal': -4.0, 'oversold_reversal': -2.0,
                    'overbought_entry': 4.5, 'overbought_signal': 4.0, 'overbought_reversal': 2.0,
                    'reversal_confirmation': 0.2  # 20% reversal confirmation
                },
                '5min': {
                    'oversold_entry': -4.0, 'oversold_signal': -3.5, 'oversold_reversal': -1.8,
                    'overbought_entry': 4.0, 'overbought_signal': 3.5, 'overbought_reversal': 1.8,
                    'reversal_confirmation': 0.2
                },
                '15min': {
                    'oversold_entry': -3.5, 'oversold_signal': -3.0, 'oversold_reversal': -1.5,
                    'overbought_entry': 3.5, 'overbought_signal': 3.0, 'overbought_reversal': 1.5,
                    'reversal_confirmation': 0.2
                },
                '30min': {
                    'oversold_entry': -3.0, 'oversold_signal': -2.5, 'oversold_reversal': -1.2,
                    'overbought_entry': 3.0, 'overbought_signal': 2.5, 'overbought_reversal': 1.2,
                    'reversal_confirmation': 0.2
                },
                '45min': {
                    'oversold_entry': -2.8, 'oversold_signal': -2.3, 'oversold_reversal': -1.0,
                    'overbought_entry': 2.8, 'overbought_signal': 2.3, 'overbought_reversal': 1.0,
                    'reversal_confirmation': 0.2
                }
            },
            
            # CCI_14 - Commodity Channel Index
            'CCI_14': {
                '1min': {
                    'oversold_entry': -150, 'oversold_signal': -120, 'oversold_reversal': -80,
                    'overbought_entry': 150, 'overbought_signal': 120, 'overbought_reversal': 80,
                    'reversal_confirmation': 0.25
                },
                '5min': {
                    'oversold_entry': -140, 'oversold_signal': -110, 'oversold_reversal': -70,
                    'overbought_entry': 140, 'overbought_signal': 110, 'overbought_reversal': 70,
                    'reversal_confirmation': 0.25
                },
                '15min': {
                    'oversold_entry': -130, 'oversold_signal': -100, 'oversold_reversal': -60,
                    'overbought_entry': 130, 'overbought_signal': 100, 'overbought_reversal': 60,
                    'reversal_confirmation': 0.25
                },
                '30min': {
                    'oversold_entry': -120, 'oversold_signal': -90, 'oversold_reversal': -50,
                    'overbought_entry': 120, 'overbought_signal': 90, 'overbought_reversal': 50,
                    'reversal_confirmation': 0.25
                },
                '45min': {
                    'oversold_entry': -110, 'oversold_signal': -85, 'oversold_reversal': -45,
                    'overbought_entry': 110, 'overbought_signal': 85, 'overbought_reversal': 45,
                    'reversal_confirmation': 0.25
                }
            },
            
            # SMI - Stochastic Momentum Index
            'SMI_5_20_5_SMIo_5_20_5_100.0': {
                '1min': {
                    'oversold_entry': -50, 'oversold_signal': -45, 'oversold_reversal': -25,
                    'overbought_entry': 50, 'overbought_signal': 45, 'overbought_reversal': 25,
                    'reversal_confirmation': 0.3
                },
                '5min': {
                    'oversold_entry': -45, 'oversold_signal': -40, 'oversold_reversal': -20,
                    'overbought_entry': 45, 'overbought_signal': 40, 'overbought_reversal': 20,
                    'reversal_confirmation': 0.3
                },
                '15min': {
                    'oversold_entry': -40, 'oversold_signal': -35, 'oversold_reversal': -15,
                    'overbought_entry': 40, 'overbought_signal': 35, 'overbought_reversal': 15,
                    'reversal_confirmation': 0.3
                },
                '30min': {
                    'oversold_entry': -35, 'oversold_signal': -30, 'oversold_reversal': -12,
                    'overbought_entry': 35, 'overbought_signal': 30, 'overbought_reversal': 12,
                    'reversal_confirmation': 0.3
                },
                '45min': {
                    'oversold_entry': -32, 'oversold_signal': -28, 'oversold_reversal': -10,
                    'overbought_entry': 32, 'overbought_signal': 28, 'overbought_reversal': 10,
                    'reversal_confirmation': 0.3
                }
            },
            
            # ACCBANDS - Acceleration Bands Upper
            'ACCBANDS_10_ACCBU_10': {
                '1min': {
                    'resistance_test': 1.02, 'resistance_signal': 1.05, 'breakout_entry': 1.08,
                    'reversal_confirmation': 0.15
                },
                '5min': {
                    'resistance_test': 1.015, 'resistance_signal': 1.04, 'breakout_entry': 1.07,
                    'reversal_confirmation': 0.15
                },
                '15min': {
                    'resistance_test': 1.01, 'resistance_signal': 1.03, 'breakout_entry': 1.06,
                    'reversal_confirmation': 0.15
                },
                '30min': {
                    'resistance_test': 1.008, 'resistance_signal': 1.025, 'breakout_entry': 1.05,
                    'reversal_confirmation': 0.15
                },
                '45min': {
                    'resistance_test': 1.006, 'resistance_signal': 1.02, 'breakout_entry': 1.045,
                    'reversal_confirmation': 0.15
                }
            },
            
            # BIAS - Bias Indicator
            'BIAS_26': {
                '1min': {
                    'oversold_entry': -12, 'oversold_signal': -8, 'oversold_reversal': -4,
                    'overbought_entry': 12, 'overbought_signal': 8, 'overbought_reversal': 4,
                    'reversal_confirmation': 0.25
                },
                '5min': {
                    'oversold_entry': -10, 'oversold_signal': -6, 'oversold_reversal': -3,
                    'overbought_entry': 10, 'overbought_signal': 6, 'overbought_reversal': 3,
                    'reversal_confirmation': 0.25
                },
                '15min': {
                    'oversold_entry': -8, 'oversold_signal': -5, 'oversold_reversal': -2.5,
                    'overbought_entry': 8, 'overbought_signal': 5, 'overbought_reversal': 2.5,
                    'reversal_confirmation': 0.25
                },
                '30min': {
                    'oversold_entry': -6, 'oversold_signal': -4, 'oversold_reversal': -2,
                    'overbought_entry': 6, 'overbought_signal': 4, 'overbought_reversal': 2,
                    'reversal_confirmation': 0.25
                },
                '45min': {
                    'oversold_entry': -5, 'oversold_signal': -3.5, 'oversold_reversal': -1.8,
                    'overbought_entry': 5, 'overbought_signal': 3.5, 'overbought_reversal': 1.8,
                    'reversal_confirmation': 0.25
                }
            },
            
            # CG - Center of Gravity
            'CG_10': {
                '1min': {
                    'strong_bullish': -2.5, 'bullish_signal': -1.5, 'bullish_reversal': -0.5,
                    'strong_bearish': 2.5, 'bearish_signal': 1.5, 'bearish_reversal': 0.5,
                    'reversal_confirmation': 0.3
                },
                '5min': {
                    'strong_bullish': -2.0, 'bullish_signal': -1.2, 'bullish_reversal': -0.4,
                    'strong_bearish': 2.0, 'bearish_signal': 1.2, 'bearish_reversal': 0.4,
                    'reversal_confirmation': 0.3
                },
                '15min': {
                    'strong_bullish': -1.5, 'bullish_signal': -1.0, 'bullish_reversal': -0.3,
                    'strong_bearish': 1.5, 'bearish_signal': 1.0, 'bearish_reversal': 0.3,
                    'reversal_confirmation': 0.3
                },
                '30min': {
                    'strong_bullish': -1.2, 'bullish_signal': -0.8, 'bullish_reversal': -0.25,
                    'strong_bearish': 1.2, 'bearish_signal': 0.8, 'bearish_reversal': 0.25,
                    'reversal_confirmation': 0.3
                },
                '45min': {
                    'strong_bullish': -1.0, 'bullish_signal': -0.6, 'bullish_reversal': -0.2,
                    'strong_bearish': 1.0, 'bearish_signal': 0.6, 'bearish_reversal': 0.2,
                    'reversal_confirmation': 0.3
                }
            }
        }
    
    def perform_advanced_data_analysis(self, timeframe_data: Dict[str, pd.DataFrame], 
                                     indicators: List[str]) -> Dict[str, Any]:
        """
        Perform advanced data analysis to optimize thresholds
        """
        print(f"\n🔬 PERFORMING ADVANCED DATA ANALYSIS")
        print("=" * 60)
        print("📊 Analyzing patterns, correlations, and price movements")
        print("🎯 Optimizing thresholds based on actual market behavior")
        
        analysis_results = {
            'threshold_optimization': {},
            'pattern_analysis': {},
            'correlation_analysis': {},
            'accuracy_metrics': {},
            'optimized_thresholds': {}
        }
        
        for indicator in indicators:
            if indicator in self.professional_thresholds:
                print(f"\n🔍 Analyzing {indicator}...")
                
                # Analyze each timeframe
                indicator_analysis = self._analyze_indicator_patterns(
                    timeframe_data, indicator
                )
                
                analysis_results['threshold_optimization'][indicator] = indicator_analysis
                
                # Optimize thresholds based on analysis
                optimized = self._optimize_thresholds(
                    timeframe_data, indicator, indicator_analysis
                )
                
                analysis_results['optimized_thresholds'][indicator] = optimized
                
                print(f"   ✅ Threshold optimization completed")
        
        return analysis_results
    
    def _analyze_indicator_patterns(self, timeframe_data: Dict[str, pd.DataFrame], 
                                  indicator: str) -> Dict[str, Any]:
        """Analyze patterns for a specific indicator"""
        
        patterns = {
            'reversal_patterns': {},
            'breakout_patterns': {},
            'correlation_with_price': {},
            'optimal_thresholds': {}
        }
        
        for timeframe, data in timeframe_data.items():
            if indicator in data.columns and 'Close' in data.columns:
                indicator_values = data[indicator].dropna()
                price_values = data['Close'].dropna()
                
                if len(indicator_values) > 50:  # Minimum data for analysis
                    # Analyze reversal patterns
                    reversals = self._find_reversal_patterns(indicator_values, price_values)
                    patterns['reversal_patterns'][timeframe] = reversals
                    
                    # Analyze breakout patterns
                    breakouts = self._find_breakout_patterns(indicator_values, price_values)
                    patterns['breakout_patterns'][timeframe] = breakouts
                    
                    # Calculate correlation with price movement
                    correlation = self._calculate_price_correlation(indicator_values, price_values)
                    patterns['correlation_with_price'][timeframe] = correlation
                    
                    # Find optimal thresholds
                    optimal = self._find_optimal_thresholds(indicator_values, price_values, timeframe)
                    patterns['optimal_thresholds'][timeframe] = optimal
        
        return patterns
    
    def _find_reversal_patterns(self, indicator_values: pd.Series, 
                              price_values: pd.Series) -> Dict[str, Any]:
        """Find reversal patterns like PGO example: -2 → -3.5 → -2.7 (20% reversal)"""
        
        reversals = {
            'bullish_reversals': [],
            'bearish_reversals': [],
            'reversal_accuracy': 0.0,
            'optimal_reversal_threshold': 0.2
        }
        
        # Find potential reversal points
        for i in range(2, len(indicator_values) - 5):
            current_val = indicator_values.iloc[i]
            prev_val = indicator_values.iloc[i-1]
            next_vals = indicator_values.iloc[i+1:i+6]
            
            # Look for oversold reversal pattern (like PGO example)
            if current_val < prev_val and len(next_vals) > 0:
                # Check if it reverses back by 20%+ 
                for j, next_val in enumerate(next_vals):
                    reversal_pct = abs(next_val - current_val) / abs(current_val) if current_val != 0 else 0
                    
                    if reversal_pct >= 0.15:  # 15%+ reversal
                        # Check if price moved in expected direction
                        if i+j+1 < len(price_values):
                            price_change = (price_values.iloc[i+j+1] - price_values.iloc[i]) / price_values.iloc[i]
                            
                            if current_val < 0 and price_change > 0.002:  # Bullish reversal
                                reversals['bullish_reversals'].append({
                                    'indicator_low': current_val,
                                    'reversal_value': next_val,
                                    'reversal_pct': reversal_pct,
                                    'price_change': price_change,
                                    'time_to_reversal': j+1
                                })
                            elif current_val > 0 and price_change < -0.002:  # Bearish reversal
                                reversals['bearish_reversals'].append({
                                    'indicator_high': current_val,
                                    'reversal_value': next_val,
                                    'reversal_pct': reversal_pct,
                                    'price_change': price_change,
                                    'time_to_reversal': j+1
                                })
                        break
        
        # Calculate accuracy
        total_reversals = len(reversals['bullish_reversals']) + len(reversals['bearish_reversals'])
        if total_reversals > 0:
            successful_reversals = sum(1 for r in reversals['bullish_reversals'] if r['price_change'] > 0.005)
            successful_reversals += sum(1 for r in reversals['bearish_reversals'] if r['price_change'] < -0.005)
            reversals['reversal_accuracy'] = successful_reversals / total_reversals
        
        return reversals
    
    def _find_breakout_patterns(self, indicator_values: pd.Series, 
                              price_values: pd.Series) -> Dict[str, Any]:
        """Find breakout patterns beyond extreme levels"""
        
        breakouts = {
            'bullish_breakouts': [],
            'bearish_breakouts': [],
            'breakout_accuracy': 0.0,
            'optimal_breakout_levels': {}
        }
        
        # Calculate percentiles for breakout levels
        p95 = indicator_values.quantile(0.95)
        p5 = indicator_values.quantile(0.05)
        p99 = indicator_values.quantile(0.99)
        p1 = indicator_values.quantile(0.01)
        
        breakouts['optimal_breakout_levels'] = {
            'upper_signal': p95,
            'upper_breakout': p99,
            'lower_signal': p5,
            'lower_breakout': p1
        }
        
        # Find actual breakouts
        for i in range(len(indicator_values) - 5):
            current_val = indicator_values.iloc[i]
            
            if current_val >= p99:  # Upper breakout
                # Check price movement in next 5 periods
                if i+5 < len(price_values):
                    price_change = (price_values.iloc[i+5] - price_values.iloc[i]) / price_values.iloc[i]
                    breakouts['bearish_breakouts'].append({
                        'indicator_value': current_val,
                        'price_change': price_change,
                        'success': price_change < -0.005
                    })
            
            elif current_val <= p1:  # Lower breakout
                if i+5 < len(price_values):
                    price_change = (price_values.iloc[i+5] - price_values.iloc[i]) / price_values.iloc[i]
                    breakouts['bullish_breakouts'].append({
                        'indicator_value': current_val,
                        'price_change': price_change,
                        'success': price_change > 0.005
                    })
        
        # Calculate accuracy
        total_breakouts = len(breakouts['bullish_breakouts']) + len(breakouts['bearish_breakouts'])
        if total_breakouts > 0:
            successful = sum(1 for b in breakouts['bullish_breakouts'] if b['success'])
            successful += sum(1 for b in breakouts['bearish_breakouts'] if b['success'])
            breakouts['breakout_accuracy'] = successful / total_breakouts
        
        return breakouts
    
    def _calculate_price_correlation(self, indicator_values: pd.Series, 
                                   price_values: pd.Series) -> Dict[str, float]:
        """Calculate correlation between indicator and price movements"""
        
        # Align the series
        min_len = min(len(indicator_values), len(price_values))
        indicator_aligned = indicator_values.iloc[:min_len]
        price_aligned = price_values.iloc[:min_len]
        
        # Calculate price changes
        price_changes = price_aligned.pct_change().dropna()
        indicator_changes = indicator_aligned.diff().dropna()
        
        # Align again after calculations
        min_len = min(len(price_changes), len(indicator_changes))
        if min_len > 10:
            correlation = np.corrcoef(
                price_changes.iloc[:min_len], 
                indicator_changes.iloc[:min_len]
            )[0, 1]
            
            return {
                'correlation': correlation if not np.isnan(correlation) else 0.0,
                'strength': 'Strong' if abs(correlation) > 0.7 else 'Moderate' if abs(correlation) > 0.4 else 'Weak'
            }
        
        return {'correlation': 0.0, 'strength': 'Insufficient Data'}
    
    def _find_optimal_thresholds(self, indicator_values: pd.Series, 
                               price_values: pd.Series, timeframe: str) -> Dict[str, float]:
        """Find optimal thresholds based on price movement prediction accuracy"""
        
        optimal = {}
        
        # Test different percentile levels
        percentiles = [0.05, 0.1, 0.15, 0.2, 0.25, 0.75, 0.8, 0.85, 0.9, 0.95]
        
        best_accuracy = 0
        best_oversold = indicator_values.quantile(0.2)
        best_overbought = indicator_values.quantile(0.8)
        
        for p_low in percentiles[:5]:  # Oversold levels
            for p_high in percentiles[5:]:  # Overbought levels
                threshold_low = indicator_values.quantile(p_low)
                threshold_high = indicator_values.quantile(p_high)
                
                # Test accuracy
                accuracy = self._test_threshold_accuracy(
                    indicator_values, price_values, threshold_low, threshold_high
                )
                
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    best_oversold = threshold_low
                    best_overbought = threshold_high
        
        optimal = {
            'oversold_threshold': best_oversold,
            'overbought_threshold': best_overbought,
            'accuracy': best_accuracy,
            'timeframe': timeframe
        }
        
        return optimal
    
    def _test_threshold_accuracy(self, indicator_values: pd.Series, price_values: pd.Series,
                               threshold_low: float, threshold_high: float) -> float:
        """Test accuracy of threshold levels"""
        
        correct_predictions = 0
        total_predictions = 0
        
        for i in range(len(indicator_values) - 5):
            current_val = indicator_values.iloc[i]
            
            if current_val <= threshold_low:  # Oversold signal
                if i+5 < len(price_values):
                    price_change = (price_values.iloc[i+5] - price_values.iloc[i]) / price_values.iloc[i]
                    if price_change > 0.002:  # Successful bullish prediction
                        correct_predictions += 1
                    total_predictions += 1
            
            elif current_val >= threshold_high:  # Overbought signal
                if i+5 < len(price_values):
                    price_change = (price_values.iloc[i+5] - price_values.iloc[i]) / price_values.iloc[i]
                    if price_change < -0.002:  # Successful bearish prediction
                        correct_predictions += 1
                    total_predictions += 1
        
        return correct_predictions / total_predictions if total_predictions > 0 else 0.0
    
    def _optimize_thresholds(self, timeframe_data: Dict[str, pd.DataFrame], 
                           indicator: str, analysis: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """Optimize thresholds based on analysis results"""
        
        optimized = {}
        
        for timeframe in timeframe_data.keys():
            if timeframe in analysis['optimal_thresholds']:
                optimal = analysis['optimal_thresholds'][timeframe]
                reversals = analysis['reversal_patterns'].get(timeframe, {})
                breakouts = analysis['breakout_patterns'].get(timeframe, {})
                
                # Start with current professional thresholds
                current = self.professional_thresholds.get(indicator, {}).get(timeframe, {})
                
                # Optimize based on data analysis
                optimized_timeframe = {}
                
                if 'oversold_threshold' in optimal:
                    optimized_timeframe['oversold_signal'] = optimal['oversold_threshold']
                    optimized_timeframe['oversold_entry'] = optimal['oversold_threshold'] * 1.2  # 20% deeper
                    optimized_timeframe['oversold_reversal'] = optimal['oversold_threshold'] * 0.6  # 40% back
                
                if 'overbought_threshold' in optimal:
                    optimized_timeframe['overbought_signal'] = optimal['overbought_threshold']
                    optimized_timeframe['overbought_entry'] = optimal['overbought_threshold'] * 1.2
                    optimized_timeframe['overbought_reversal'] = optimal['overbought_threshold'] * 0.6
                
                # Add reversal confirmation based on analysis
                if reversals.get('reversal_accuracy', 0) > 0.6:
                    optimized_timeframe['reversal_confirmation'] = 0.15  # 15% for high accuracy
                else:
                    optimized_timeframe['reversal_confirmation'] = 0.25  # 25% for lower accuracy
                
                optimized[timeframe] = optimized_timeframe
        
        return optimized

    def detect_professional_signals_with_timing(self, timeframe_data: Dict[str, pd.DataFrame],
                                               indicators: List[str]) -> Dict[str, Any]:
        """
        Detect professional signals with time-based tracking
        Like PGO: -2 → -3.5 → -2.7 (20% reversal) = BUY signal
        """
        print(f"\n🎯 PROFESSIONAL SIGNAL DETECTION WITH TIMING")
        print("=" * 60)
        print("⏰ Tracking signal generation and confirmation times")
        print("🔄 Detecting reversal patterns like PGO example")

        signal_results = {
            'signals_by_indicator': {},
            'signal_timeline': {},
            'confirmation_tracking': {},
            'entry_points': {}
        }

        for indicator in indicators:
            if indicator in self.professional_thresholds:
                print(f"\n🔍 Analyzing {indicator} signals...")

                indicator_signals = self._detect_indicator_signals_with_timing(
                    timeframe_data, indicator
                )

                signal_results['signals_by_indicator'][indicator] = indicator_signals

                # Track confirmations across timeframes
                confirmations = self._track_signal_confirmations(
                    timeframe_data, indicator, indicator_signals
                )

                signal_results['confirmation_tracking'][indicator] = confirmations

                # Find entry points
                entry_points = self._find_entry_points(
                    timeframe_data, indicator, indicator_signals
                )

                signal_results['entry_points'][indicator] = entry_points

                print(f"   ✅ Found {len(indicator_signals)} signals with timing")

        return signal_results

    def _detect_indicator_signals_with_timing(self, timeframe_data: Dict[str, pd.DataFrame],
                                            indicator: str) -> List[Dict[str, Any]]:
        """Detect signals for a specific indicator with timing"""

        signals = []

        # Sort timeframes by duration (highest to lowest)
        timeframe_order = sorted(timeframe_data.keys(),
                               key=lambda x: int(x.replace('min', '')),
                               reverse=True)

        for timeframe in timeframe_order:
            if indicator in timeframe_data[timeframe].columns:
                data = timeframe_data[timeframe]
                indicator_values = data[indicator].dropna()

                if len(indicator_values) > 10:
                    timeframe_signals = self._detect_timeframe_signals(
                        indicator_values, data, indicator, timeframe
                    )
                    signals.extend(timeframe_signals)

        # Sort signals by time
        signals.sort(key=lambda x: x.get('timestamp', ''))

        return signals

    def _detect_timeframe_signals(self, indicator_values: pd.Series, data: pd.DataFrame,
                                indicator: str, timeframe: str) -> List[Dict[str, Any]]:
        """Detect signals for a specific timeframe"""

        signals = []
        thresholds = self.professional_thresholds.get(indicator, {}).get(timeframe, {})

        if not thresholds:
            return signals

        # Get price data for validation
        price_col = None
        for col in ['Close', 'CURRENT_PRICE', 'HLC3', 'OHLC4']:
            if col in data.columns:
                price_col = col
                break

        if price_col is None:
            return signals

        price_values = data[price_col].dropna()

        # Detect different types of signals
        if indicator == 'PGO_14':
            signals.extend(self._detect_pgo_reversal_signals(
                indicator_values, price_values, thresholds, timeframe
            ))
        else:
            signals.extend(self._detect_standard_signals(
                indicator_values, price_values, thresholds, timeframe, indicator
            ))

        return signals

    def _detect_pgo_reversal_signals(self, indicator_values: pd.Series, price_values: pd.Series,
                                   thresholds: Dict[str, float], timeframe: str) -> List[Dict[str, Any]]:
        """
        Detect PGO-style reversal signals
        Example: -2 → -3.5 → -2.7 (20% reversal) = BUY signal
        """

        signals = []

        oversold_signal = thresholds.get('oversold_signal', -3.0)
        oversold_entry = thresholds.get('oversold_entry', -3.5)
        oversold_reversal = thresholds.get('oversold_reversal', -1.5)
        reversal_confirmation = thresholds.get('reversal_confirmation', 0.2)

        overbought_signal = thresholds.get('overbought_signal', 3.0)
        overbought_entry = thresholds.get('overbought_entry', 3.5)
        overbought_reversal = thresholds.get('overbought_reversal', 1.5)

        for i in range(5, len(indicator_values) - 5):
            current_val = indicator_values.iloc[i]

            # Look for oversold reversal pattern
            if current_val <= oversold_signal:
                # Check if it went deeper (entry level)
                deeper_found = False
                deepest_val = current_val
                deepest_idx = i

                # Look back 5 periods for deeper penetration
                for j in range(max(0, i-5), i):
                    if indicator_values.iloc[j] <= oversold_entry:
                        deeper_found = True
                        if indicator_values.iloc[j] < deepest_val:
                            deepest_val = indicator_values.iloc[j]
                            deepest_idx = j

                if deeper_found:
                    # Look forward for reversal
                    for k in range(i+1, min(len(indicator_values), i+6)):
                        reversal_val = indicator_values.iloc[k]

                        # Calculate reversal percentage
                        if deepest_val != 0:
                            reversal_pct = abs(reversal_val - deepest_val) / abs(deepest_val)

                            if reversal_pct >= reversal_confirmation and reversal_val > oversold_reversal:
                                # Found reversal pattern - BUY signal
                                signal_time = indicator_values.index[k] if hasattr(indicator_values.index, 'strftime') else f"Period_{k}"

                                signals.append({
                                    'signal_type': 'BUY',
                                    'signal_reason': 'PGO_REVERSAL_PATTERN',
                                    'timeframe': timeframe,
                                    'timestamp': str(signal_time),
                                    'signal_generation_time': str(indicator_values.index[deepest_idx]) if hasattr(indicator_values.index, 'strftime') else f"Period_{deepest_idx}",
                                    'confirmation_time': str(signal_time),
                                    'deepest_value': deepest_val,
                                    'reversal_value': reversal_val,
                                    'reversal_percentage': reversal_pct,
                                    'signal_strength': min(reversal_pct / reversal_confirmation, 1.0),
                                    'entry_level': oversold_reversal,
                                    'professional_assessment': f"PGO reversal: {deepest_val:.2f} → {reversal_val:.2f} ({reversal_pct:.1%} reversal)"
                                })
                                break

            # Look for overbought reversal pattern (similar logic)
            elif current_val >= overbought_signal:
                deeper_found = False
                highest_val = current_val
                highest_idx = i

                for j in range(max(0, i-5), i):
                    if indicator_values.iloc[j] >= overbought_entry:
                        deeper_found = True
                        if indicator_values.iloc[j] > highest_val:
                            highest_val = indicator_values.iloc[j]
                            highest_idx = j

                if deeper_found:
                    for k in range(i+1, min(len(indicator_values), i+6)):
                        reversal_val = indicator_values.iloc[k]

                        if highest_val != 0:
                            reversal_pct = abs(reversal_val - highest_val) / abs(highest_val)

                            if reversal_pct >= reversal_confirmation and reversal_val < overbought_reversal:
                                signal_time = indicator_values.index[k] if hasattr(indicator_values.index, 'strftime') else f"Period_{k}"

                                signals.append({
                                    'signal_type': 'SELL',
                                    'signal_reason': 'PGO_REVERSAL_PATTERN',
                                    'timeframe': timeframe,
                                    'timestamp': str(signal_time),
                                    'signal_generation_time': str(indicator_values.index[highest_idx]) if hasattr(indicator_values.index, 'strftime') else f"Period_{highest_idx}",
                                    'confirmation_time': str(signal_time),
                                    'highest_value': highest_val,
                                    'reversal_value': reversal_val,
                                    'reversal_percentage': reversal_pct,
                                    'signal_strength': min(reversal_pct / reversal_confirmation, 1.0),
                                    'entry_level': overbought_reversal,
                                    'professional_assessment': f"PGO reversal: {highest_val:.2f} → {reversal_val:.2f} ({reversal_pct:.1%} reversal)"
                                })
                                break

        return signals

    def _detect_standard_signals(self, indicator_values: pd.Series, price_values: pd.Series,
                               thresholds: Dict[str, float], timeframe: str,
                               indicator: str) -> List[Dict[str, Any]]:
        """Detect standard overbought/oversold signals with timing"""

        signals = []

        # Get threshold values
        oversold_signal = thresholds.get('oversold_signal', -100)
        oversold_entry = thresholds.get('oversold_entry', -120)
        oversold_reversal = thresholds.get('oversold_reversal', -60)

        overbought_signal = thresholds.get('overbought_signal', 100)
        overbought_entry = thresholds.get('overbought_entry', 120)
        overbought_reversal = thresholds.get('overbought_reversal', 60)

        reversal_confirmation = thresholds.get('reversal_confirmation', 0.25)

        for i in range(2, len(indicator_values) - 3):
            current_val = indicator_values.iloc[i]
            prev_val = indicator_values.iloc[i-1]

            # Oversold signal detection
            if current_val <= oversold_signal and prev_val > oversold_signal:
                signal_time = indicator_values.index[i] if hasattr(indicator_values.index, 'strftime') else f"Period_{i}"

                # Look for confirmation in next periods
                confirmation_found = False
                confirmation_time = None

                for j in range(i+1, min(len(indicator_values), i+4)):
                    if indicator_values.iloc[j] > oversold_reversal:
                        confirmation_found = True
                        confirmation_time = indicator_values.index[j] if hasattr(indicator_values.index, 'strftime') else f"Period_{j}"
                        break

                signals.append({
                    'signal_type': 'BUY',
                    'signal_reason': 'OVERSOLD_SIGNAL',
                    'timeframe': timeframe,
                    'timestamp': str(signal_time),
                    'signal_generation_time': str(signal_time),
                    'confirmation_time': str(confirmation_time) if confirmation_found else 'PENDING',
                    'indicator_value': current_val,
                    'signal_strength': min(abs(current_val - oversold_signal) / abs(oversold_entry - oversold_signal), 1.0),
                    'entry_level': oversold_reversal,
                    'confirmed': confirmation_found,
                    'professional_assessment': f"{indicator} oversold at {current_val:.2f} in {timeframe}"
                })

            # Overbought signal detection
            elif current_val >= overbought_signal and prev_val < overbought_signal:
                signal_time = indicator_values.index[i] if hasattr(indicator_values.index, 'strftime') else f"Period_{i}"

                confirmation_found = False
                confirmation_time = None

                for j in range(i+1, min(len(indicator_values), i+4)):
                    if indicator_values.iloc[j] < overbought_reversal:
                        confirmation_found = True
                        confirmation_time = indicator_values.index[j] if hasattr(indicator_values.index, 'strftime') else f"Period_{j}"
                        break

                signals.append({
                    'signal_type': 'SELL',
                    'signal_reason': 'OVERBOUGHT_SIGNAL',
                    'timeframe': timeframe,
                    'timestamp': str(signal_time),
                    'signal_generation_time': str(signal_time),
                    'confirmation_time': str(confirmation_time) if confirmation_found else 'PENDING',
                    'indicator_value': current_val,
                    'signal_strength': min(abs(current_val - overbought_signal) / abs(overbought_entry - overbought_signal), 1.0),
                    'entry_level': overbought_reversal,
                    'confirmed': confirmation_found,
                    'professional_assessment': f"{indicator} overbought at {current_val:.2f} in {timeframe}"
                })

        return signals

    def _track_signal_confirmations(self, timeframe_data: Dict[str, pd.DataFrame],
                                   indicator: str, signals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Track signal confirmations across timeframes"""

        confirmations = {
            'hierarchical_confirmations': [],
            'confirmation_accuracy': 0.0,
            'timeframe_alignment': {}
        }

        # Sort timeframes by duration
        timeframe_order = sorted(timeframe_data.keys(),
                               key=lambda x: int(x.replace('min', '')),
                               reverse=True)

        # Group signals by timeframe
        signals_by_timeframe = {}
        for signal in signals:
            tf = signal['timeframe']
            if tf not in signals_by_timeframe:
                signals_by_timeframe[tf] = []
            signals_by_timeframe[tf].append(signal)

        # Check for hierarchical confirmations
        for higher_tf in timeframe_order:
            if higher_tf in signals_by_timeframe:
                higher_signals = signals_by_timeframe[higher_tf]

                for higher_signal in higher_signals:
                    confirmation_chain = {
                        'higher_timeframe_signal': higher_signal,
                        'lower_timeframe_confirmations': [],
                        'confirmation_strength': 0.0,
                        'entry_recommendation': 'HOLD'
                    }

                    # Look for confirmations in lower timeframes
                    for lower_tf in timeframe_order:
                        if lower_tf != higher_tf and lower_tf in signals_by_timeframe:
                            lower_signals = signals_by_timeframe[lower_tf]

                            for lower_signal in lower_signals:
                                # Check if signals align
                                if (higher_signal['signal_type'] == lower_signal['signal_type'] and
                                    self._signals_time_aligned(higher_signal, lower_signal)):

                                    confirmation_chain['lower_timeframe_confirmations'].append({
                                        'timeframe': lower_tf,
                                        'signal': lower_signal,
                                        'time_alignment': 'CONFIRMED'
                                    })

                    # Calculate confirmation strength
                    total_lower_timeframes = len([tf for tf in timeframe_order if tf != higher_tf])
                    confirmations_count = len(confirmation_chain['lower_timeframe_confirmations'])

                    if total_lower_timeframes > 0:
                        confirmation_chain['confirmation_strength'] = confirmations_count / total_lower_timeframes

                    # Generate entry recommendation
                    if confirmation_chain['confirmation_strength'] >= 0.7:
                        confirmation_chain['entry_recommendation'] = f"STRONG {higher_signal['signal_type']}"
                    elif confirmation_chain['confirmation_strength'] >= 0.5:
                        confirmation_chain['entry_recommendation'] = f"MODERATE {higher_signal['signal_type']}"
                    else:
                        confirmation_chain['entry_recommendation'] = "HOLD"

                    confirmations['hierarchical_confirmations'].append(confirmation_chain)

        return confirmations

    def _signals_time_aligned(self, higher_signal: Dict[str, Any],
                            lower_signal: Dict[str, Any]) -> bool:
        """Check if signals are time-aligned"""

        # For now, simple check - can be enhanced with actual time parsing
        higher_time = higher_signal.get('timestamp', '')
        lower_time = lower_signal.get('timestamp', '')

        # If both have timestamps, they should be reasonably close
        # For demo purposes, we'll consider them aligned if they exist
        return bool(higher_time and lower_time)

    def _find_entry_points(self, timeframe_data: Dict[str, pd.DataFrame],
                          indicator: str, signals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Find optimal entry points based on signals"""

        entry_points = {
            'immediate_entries': [],
            'pending_entries': [],
            'entry_strategy': {}
        }

        # Find 1-minute timeframe for entry precision
        one_min_data = None
        for tf, data in timeframe_data.items():
            if '1min' in tf or tf == '1':
                one_min_data = data
                break

        if one_min_data is None:
            return entry_points

        # Process each signal for entry points
        for signal in signals:
            if signal['signal_type'] in ['BUY', 'SELL']:
                entry_point = self._calculate_entry_point(signal, one_min_data, indicator)

                if entry_point['entry_status'] == 'IMMEDIATE':
                    entry_points['immediate_entries'].append(entry_point)
                else:
                    entry_points['pending_entries'].append(entry_point)

        # Generate entry strategy
        entry_points['entry_strategy'] = self._generate_entry_strategy(
            entry_points['immediate_entries'],
            entry_points['pending_entries']
        )

        return entry_points

    def _calculate_entry_point(self, signal: Dict[str, Any], one_min_data: pd.DataFrame,
                             indicator: str) -> Dict[str, Any]:
        """Calculate specific entry point for a signal"""

        entry_point = {
            'signal_reference': signal,
            'entry_level': signal.get('entry_level', 0),
            'current_level': 0,
            'entry_status': 'PENDING',
            'entry_timing': 'WAIT_FOR_CONFIRMATION',
            'risk_reward_ratio': 0,
            'stop_loss': 0,
            'target': 0
        }

        if indicator in one_min_data.columns:
            current_value = one_min_data[indicator].dropna().iloc[-1] if len(one_min_data[indicator].dropna()) > 0 else 0
            entry_point['current_level'] = current_value

            # Check if entry level is reached
            if signal['signal_type'] == 'BUY':
                if current_value <= signal.get('entry_level', float('inf')):
                    entry_point['entry_status'] = 'IMMEDIATE'
                    entry_point['entry_timing'] = 'ENTER_NOW'

                    # Calculate stop loss and target
                    entry_point['stop_loss'] = current_value * 0.98  # 2% stop loss
                    entry_point['target'] = current_value * 1.06     # 6% target
                    entry_point['risk_reward_ratio'] = 3.0

            elif signal['signal_type'] == 'SELL':
                if current_value >= signal.get('entry_level', float('-inf')):
                    entry_point['entry_status'] = 'IMMEDIATE'
                    entry_point['entry_timing'] = 'ENTER_NOW'

                    entry_point['stop_loss'] = current_value * 1.02  # 2% stop loss
                    entry_point['target'] = current_value * 0.94     # 6% target
                    entry_point['risk_reward_ratio'] = 3.0

        return entry_point

    def _generate_entry_strategy(self, immediate_entries: List[Dict[str, Any]],
                               pending_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate overall entry strategy"""

        strategy = {
            'immediate_action': 'HOLD',
            'priority_signals': [],
            'risk_assessment': 'MODERATE',
            'position_sizing': 'STANDARD',
            'overall_recommendation': 'WAIT'
        }

        if immediate_entries:
            # Sort by signal strength
            immediate_entries.sort(key=lambda x: x['signal_reference'].get('signal_strength', 0), reverse=True)

            strongest_signal = immediate_entries[0]
            strategy['immediate_action'] = strongest_signal['entry_timing']
            strategy['priority_signals'] = immediate_entries[:3]  # Top 3 signals

            # Assess overall risk
            buy_signals = sum(1 for entry in immediate_entries if entry['signal_reference']['signal_type'] == 'BUY')
            sell_signals = sum(1 for entry in immediate_entries if entry['signal_reference']['signal_type'] == 'SELL')

            if buy_signals > sell_signals * 2:
                strategy['overall_recommendation'] = 'STRONG_BUY'
                strategy['risk_assessment'] = 'LOW'
            elif sell_signals > buy_signals * 2:
                strategy['overall_recommendation'] = 'STRONG_SELL'
                strategy['risk_assessment'] = 'LOW'
            elif abs(buy_signals - sell_signals) <= 1:
                strategy['overall_recommendation'] = 'NEUTRAL'
                strategy['risk_assessment'] = 'HIGH'
            else:
                strategy['overall_recommendation'] = 'MODERATE_BIAS'
                strategy['risk_assessment'] = 'MODERATE'

        return strategy

    def export_advanced_analysis_to_excel(self, analysis_results: Dict[str, Any],
                                         signal_results: Dict[str, Any],
                                         inputs: Dict[str, Any]) -> str:
        """Export advanced analysis results to Excel"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"advanced_professional_analysis_{inputs['ticker']}_{inputs['exchange']}_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # Sheet 1: Signal Timeline
                timeline_data = []
                for indicator, signals in signal_results['signals_by_indicator'].items():
                    for signal in signals:
                        timeline_data.append({
                            'Indicator': indicator,
                            'Signal_Type': signal['signal_type'],
                            'Signal_Reason': signal['signal_reason'],
                            'Timeframe': signal['timeframe'],
                            'Generation_Time': signal['signal_generation_time'],
                            'Confirmation_Time': signal['confirmation_time'],
                            'Signal_Strength': signal['signal_strength'],
                            'Entry_Level': signal.get('entry_level', 0),
                            'Confirmed': signal.get('confirmed', False),
                            'Professional_Assessment': signal['professional_assessment']
                        })

                if timeline_data:
                    timeline_df = pd.DataFrame(timeline_data)
                    timeline_df = timeline_df.sort_values(['Generation_Time', 'Signal_Strength'], ascending=[True, False])
                    timeline_df.to_excel(writer, sheet_name='Signal_Timeline', index=False)

                # Sheet 2: Hierarchical Confirmations
                confirmation_data = []
                for indicator, confirmations in signal_results['confirmation_tracking'].items():
                    for conf in confirmations['hierarchical_confirmations']:
                        higher_signal = conf['higher_timeframe_signal']
                        confirmation_data.append({
                            'Indicator': indicator,
                            'Higher_Timeframe': higher_signal['timeframe'],
                            'Signal_Type': higher_signal['signal_type'],
                            'Signal_Strength': higher_signal['signal_strength'],
                            'Confirmations_Count': len(conf['lower_timeframe_confirmations']),
                            'Confirmation_Strength': conf['confirmation_strength'],
                            'Entry_Recommendation': conf['entry_recommendation'],
                            'Generation_Time': higher_signal['signal_generation_time'],
                            'Confirmation_Time': higher_signal['confirmation_time']
                        })

                if confirmation_data:
                    confirmation_df = pd.DataFrame(confirmation_data)
                    confirmation_df = confirmation_df.sort_values('Confirmation_Strength', ascending=False)
                    confirmation_df.to_excel(writer, sheet_name='Hierarchical_Confirmations', index=False)

                # Sheet 3: Entry Points
                entry_data = []
                for indicator, entries in signal_results['entry_points'].items():
                    for entry in entries['immediate_entries'] + entries['pending_entries']:
                        signal_ref = entry['signal_reference']
                        entry_data.append({
                            'Indicator': indicator,
                            'Signal_Type': signal_ref['signal_type'],
                            'Timeframe': signal_ref['timeframe'],
                            'Entry_Status': entry['entry_status'],
                            'Entry_Timing': entry['entry_timing'],
                            'Current_Level': entry['current_level'],
                            'Entry_Level': entry['entry_level'],
                            'Stop_Loss': entry['stop_loss'],
                            'Target': entry['target'],
                            'Risk_Reward_Ratio': entry['risk_reward_ratio'],
                            'Signal_Strength': signal_ref['signal_strength']
                        })

                if entry_data:
                    entry_df = pd.DataFrame(entry_data)
                    entry_df = entry_df.sort_values(['Entry_Status', 'Signal_Strength'], ascending=[True, False])
                    entry_df.to_excel(writer, sheet_name='Entry_Points', index=False)

                # Sheet 4: Threshold Optimization Results
                optimization_data = []
                for indicator, analysis in analysis_results['threshold_optimization'].items():
                    for timeframe, optimal in analysis.get('optimal_thresholds', {}).items():
                        optimization_data.append({
                            'Indicator': indicator,
                            'Timeframe': timeframe,
                            'Optimized_Oversold': optimal.get('oversold_threshold', 0),
                            'Optimized_Overbought': optimal.get('overbought_threshold', 0),
                            'Accuracy': optimal.get('accuracy', 0),
                            'Reversal_Accuracy': analysis.get('reversal_patterns', {}).get(timeframe, {}).get('reversal_accuracy', 0),
                            'Breakout_Accuracy': analysis.get('breakout_patterns', {}).get(timeframe, {}).get('breakout_accuracy', 0)
                        })

                if optimization_data:
                    optimization_df = pd.DataFrame(optimization_data)
                    optimization_df = optimization_df.sort_values('Accuracy', ascending=False)
                    optimization_df.to_excel(writer, sheet_name='Threshold_Optimization', index=False)

                # Sheet 5: PGO Reversal Patterns
                pgo_data = []
                for indicator, signals in signal_results['signals_by_indicator'].items():
                    if indicator == 'PGO_14':
                        for signal in signals:
                            if signal['signal_reason'] == 'PGO_REVERSAL_PATTERN':
                                pgo_data.append({
                                    'Timeframe': signal['timeframe'],
                                    'Signal_Type': signal['signal_type'],
                                    'Deepest_Value': signal.get('deepest_value', signal.get('highest_value', 0)),
                                    'Reversal_Value': signal['reversal_value'],
                                    'Reversal_Percentage': signal['reversal_percentage'],
                                    'Generation_Time': signal['signal_generation_time'],
                                    'Confirmation_Time': signal['confirmation_time'],
                                    'Signal_Strength': signal['signal_strength'],
                                    'Professional_Assessment': signal['professional_assessment']
                                })

                if pgo_data:
                    pgo_df = pd.DataFrame(pgo_data)
                    pgo_df = pgo_df.sort_values('Reversal_Percentage', ascending=False)
                    pgo_df.to_excel(writer, sheet_name='PGO_Reversal_Patterns', index=False)

                # Sheet 6: Strong Signals Summary
                strong_signals_data = []
                for indicator, confirmations in signal_results['confirmation_tracking'].items():
                    for conf in confirmations['hierarchical_confirmations']:
                        if conf['confirmation_strength'] >= 0.7:
                            higher_signal = conf['higher_timeframe_signal']
                            strong_signals_data.append({
                                'Indicator': indicator,
                                'Signal_Type': higher_signal['signal_type'],
                                'Timeframe': higher_signal['timeframe'],
                                'Signal_Strength': higher_signal['signal_strength'],
                                'Confirmation_Strength': conf['confirmation_strength'],
                                'Entry_Recommendation': conf['entry_recommendation'],
                                'Generation_Time': higher_signal['signal_generation_time'],
                                'Professional_Assessment': higher_signal['professional_assessment']
                            })

                if strong_signals_data:
                    strong_df = pd.DataFrame(strong_signals_data)
                    strong_df = strong_df.sort_values(['Confirmation_Strength', 'Signal_Strength'], ascending=[False, False])
                    strong_df.to_excel(writer, sheet_name='Strong_Signals_Summary', index=False)

            print(f"✅ Advanced analysis Excel file saved: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error exporting to Excel: {str(e)}")
            return ""

    def run_complete_advanced_analysis(self) -> Dict[str, Any]:
        """Run complete advanced professional signal analysis"""

        print("\n🚀 ADVANCED PROFESSIONAL SIGNAL ANALYZER")
        print("=" * 80)
        print("⏰ Time-based signal generation and confirmation tracking")
        print("🔄 PGO-style reversal detection with professional thresholds")
        print("📊 Data-driven threshold optimization")
        print("🎯 Professional timeframe-adjusted signal values")

        # Get user inputs
        inputs = self._get_user_inputs()

        # Find and load data
        timeframe_data = self._load_timeframe_data(inputs)

        if len(timeframe_data) < 2:
            print("❌ Need at least 2 timeframes for analysis")
            return {}

        # Filter indicators
        available_indicators = self._filter_available_indicators(inputs['indicators'], timeframe_data)

        if not available_indicators:
            print("❌ No configured indicators found")
            return {}

        print(f"\n📊 Analyzing {len(available_indicators)} professional indicators:")
        for indicator in available_indicators:
            print(f"   ✅ {indicator}")

        # Perform advanced data analysis
        analysis_results = self.perform_advanced_data_analysis(timeframe_data, available_indicators)

        # Detect professional signals with timing
        signal_results = self.detect_professional_signals_with_timing(timeframe_data, available_indicators)

        # Export to Excel
        excel_filename = self.export_advanced_analysis_to_excel(analysis_results, signal_results, inputs)

        # Print comprehensive summary
        self._print_advanced_summary(analysis_results, signal_results)

        # Save JSON backup
        json_filename = f"advanced_analysis_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_filename, 'w') as f:
            json.dump({
                'analysis_results': analysis_results,
                'signal_results': signal_results,
                'inputs': inputs
            }, f, indent=2, default=str)

        print(f"\n💾 Results saved:")
        print(f"   📊 Excel: {excel_filename}")
        print(f"   📄 JSON: {json_filename}")

        return {
            'analysis_results': analysis_results,
            'signal_results': signal_results,
            'inputs': inputs
        }

    def _get_user_inputs(self) -> Dict[str, Any]:
        """Get user inputs for analysis"""

        print(f"\n📝 ENTER ANALYSIS PARAMETERS")
        print("=" * 50)

        ticker = input("📊 Enter ticker (e.g., NATURALGAS26AUG25): ").strip().upper()
        exchange = input("🏢 Enter exchange (NSE/BSE/MCX/NFO): ").strip().upper()
        date = input("📅 Enter date (DD-MM-YYYY): ").strip()

        print("\n⏰ Available timeframes: 1, 5, 15, 30, 45, 60 minutes")
        timeframes_input = input("Enter timeframes (comma-separated, e.g., 1,5,15,30): ").strip()
        timeframes = [tf.strip() for tf in timeframes_input.split(',')]

        print(f"\n🔍 Your specific indicators available:")
        available_indicators = list(self.professional_thresholds.keys())
        for i, indicator in enumerate(available_indicators, 1):
            print(f"   {i:2d}. {indicator}")

        indicators_input = input("\nEnter indicators (comma-separated names or 'all'): ").strip()
        if indicators_input.lower() == 'all':
            indicators = available_indicators
        else:
            indicators = [ind.strip() for ind in indicators_input.split(',')]

        validate_signals = input("\n✅ Validate signals with price movement? (y/n): ").strip().lower() == 'y'

        return {
            'ticker': ticker,
            'exchange': exchange,
            'date': date,
            'timeframes': timeframes,
            'indicators': indicators,
            'validate_signals': validate_signals
        }

    def _load_timeframe_data(self, inputs: Dict[str, Any]) -> Dict[str, pd.DataFrame]:
        """Load timeframe data from files"""

        print(f"\n🔍 SEARCHING FOR DATA FILES")
        print("=" * 50)

        # Search for data files
        search_pattern = f"*{inputs['ticker']}*{inputs['exchange']}*.xlsx"
        data_files = glob.glob(search_pattern)

        if not data_files:
            print(f"❌ No files found matching pattern: {search_pattern}")
            return {}

        print(f"✅ Found {len(data_files)} data files")
        for file in data_files:
            print(f"   📄 {os.path.basename(file)}")

        # Assign timeframes to files
        timeframe_files = {}
        for timeframe in inputs['timeframes']:
            # Find best matching file for this timeframe
            best_file = None
            for file in data_files:
                if f"{timeframe}min" in file or f"_{timeframe}_" in file:
                    best_file = file
                    break

            if not best_file:
                # Use any available file
                best_file = data_files[0] if data_files else None

            if best_file:
                timeframe_files[f"{timeframe}min"] = best_file

        # Load data
        timeframe_data = {}
        print(f"\n📂 LOADING TIMEFRAME DATA")
        print("=" * 50)

        for timeframe, file_path in timeframe_files.items():
            try:
                df = pd.read_excel(file_path)
                timeframe_data[timeframe] = df
                print(f"✅ {timeframe}: {df.shape}")
            except Exception as e:
                print(f"❌ Error loading {timeframe}: {str(e)}")

        return timeframe_data

    def _filter_available_indicators(self, requested_indicators: List[str],
                                   timeframe_data: Dict[str, pd.DataFrame]) -> List[str]:
        """Filter indicators that are available in data and configured"""

        available_indicators = []

        # Get all columns from all timeframes
        all_columns = set()
        for df in timeframe_data.values():
            all_columns.update(df.columns)

        for indicator in requested_indicators:
            if indicator in self.professional_thresholds:
                # Check if indicator exists in data
                if indicator in all_columns:
                    available_indicators.append(indicator)
                else:
                    # Try partial matching
                    for col in all_columns:
                        if indicator in col or col in indicator:
                            available_indicators.append(indicator)
                            print(f"🔍 Matched '{indicator}' → '{col}'")
                            break

        return available_indicators

    def _print_advanced_summary(self, analysis_results: Dict[str, Any],
                              signal_results: Dict[str, Any]):
        """Print comprehensive summary of advanced analysis"""

        print(f"\n📊 ADVANCED PROFESSIONAL ANALYSIS SUMMARY")
        print("=" * 80)

        # Count signals by type
        total_signals = 0
        buy_signals = 0
        sell_signals = 0
        reversal_patterns = 0
        confirmed_signals = 0

        for indicator, signals in signal_results['signals_by_indicator'].items():
            total_signals += len(signals)
            for signal in signals:
                if signal['signal_type'] == 'BUY':
                    buy_signals += 1
                elif signal['signal_type'] == 'SELL':
                    sell_signals += 1

                if 'REVERSAL' in signal['signal_reason']:
                    reversal_patterns += 1

                if signal.get('confirmed', False):
                    confirmed_signals += 1

        print(f"🎯 SIGNAL SUMMARY:")
        print(f"   📊 Total Signals: {total_signals}")
        print(f"   🟢 Buy Signals: {buy_signals}")
        print(f"   🔴 Sell Signals: {sell_signals}")
        print(f"   🔄 Reversal Patterns: {reversal_patterns}")
        print(f"   ✅ Confirmed Signals: {confirmed_signals}")

        # Show top signals with timing
        print(f"\n🏆 TOP PROFESSIONAL SIGNALS WITH TIMING:")

        all_signals = []
        for indicator, signals in signal_results['signals_by_indicator'].items():
            for signal in signals:
                signal['indicator'] = indicator
                all_signals.append(signal)

        # Sort by signal strength
        all_signals.sort(key=lambda x: x.get('signal_strength', 0), reverse=True)

        for i, signal in enumerate(all_signals[:10]):
            print(f"\n   {i+1:2d}. {signal['indicator']} - {signal['signal_type']}")
            print(f"       Reason: {signal['signal_reason']}")
            print(f"       Timeframe: {signal['timeframe']}")
            print(f"       Generation Time: {signal['signal_generation_time']}")
            print(f"       Confirmation Time: {signal['confirmation_time']}")
            print(f"       Strength: {signal['signal_strength']:.3f}")
            print(f"       Assessment: {signal['professional_assessment']}")

        # Show hierarchical confirmations
        print(f"\n🎯 HIERARCHICAL CONFIRMATIONS:")

        strong_confirmations = 0
        for indicator, confirmations in signal_results['confirmation_tracking'].items():
            for conf in confirmations['hierarchical_confirmations']:
                if conf['confirmation_strength'] >= 0.7:
                    strong_confirmations += 1
                    higher_signal = conf['higher_timeframe_signal']
                    print(f"\n   ✅ {indicator} - {conf['entry_recommendation']}")
                    print(f"      Higher TF: {higher_signal['timeframe']} ({higher_signal['signal_type']})")
                    print(f"      Confirmations: {len(conf['lower_timeframe_confirmations'])}")
                    print(f"      Strength: {conf['confirmation_strength']:.1%}")

        if strong_confirmations == 0:
            print("   ⚪ No strong hierarchical confirmations found")

        # Show threshold optimization results
        print(f"\n📈 THRESHOLD OPTIMIZATION RESULTS:")

        for indicator, analysis in analysis_results['threshold_optimization'].items():
            best_accuracy = 0
            best_timeframe = ""

            for timeframe, optimal in analysis.get('optimal_thresholds', {}).items():
                accuracy = optimal.get('accuracy', 0)
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    best_timeframe = timeframe

            if best_accuracy > 0:
                print(f"   📊 {indicator}: Best accuracy {best_accuracy:.1%} in {best_timeframe}")


def main():
    """Main execution function"""

    analyzer = AdvancedProfessionalSignalAnalyzer()
    results = analyzer.run_complete_advanced_analysis()

    if results:
        print("\n✅ Advanced Professional Signal Analysis completed successfully!")
        print("⏰ Time-based signal generation and confirmation tracking completed")
        print("🔄 PGO-style reversal detection with professional thresholds")
        print("📊 Data-driven threshold optimization completed")
        print("🎯 Professional timeframe-adjusted signal values applied")
        print("📄 Check the Excel file for detailed advanced analysis!")
    else:
        print("\n❌ Analysis failed.")


if __name__ == "__main__":
    main()
