"""
Simple Multi-Timeframe Analyzer

Uses existing files to perform multi-timeframe confluence analysis
without generating new data.
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

class SimpleMultiTimeframeAnalyzer:
    """
    Simple multi-timeframe analyzer using existing files
    """
    
    def __init__(self):
        """Initialize the analyzer"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        print("🚀 Simple Multi-Timeframe Analyzer initialized")
    
    def find_usable_files(self) -> Dict[str, str]:
        """Find existing usable files for multi-timeframe analysis"""
        usable_files = {}
        
        print(f"\n🔍 SEARCHING FOR USABLE FILES")
        print("=" * 50)
        
        # Find all Natural Gas files with Time_Series_Indicators
        for file in os.listdir(self.current_dir):
            if ('NATURALGAS26AUG25' in file and 
                file.endswith('.xlsx') and 
                not file.startswith('~$')):
                
                try:
                    filepath = os.path.join(self.current_dir, file)
                    excel_file = pd.ExcelFile(filepath)
                    
                    if 'Time_Series_Indicators' in excel_file.sheet_names:
                        df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                        
                        if df.shape[1] > 50:  # Should have many indicators
                            usable_files[file] = {
                                'shape': df.shape,
                                'indicators': df.shape[1]
                            }
                            print(f"✅ Found: {file} ({df.shape[1]} indicators, {df.shape[0]} time points)")
                        
                except Exception as e:
                    print(f"⚠️ Error checking {file}: {str(e)}")
        
        return usable_files
    
    def assign_timeframes(self, usable_files: Dict[str, Dict]) -> Dict[str, str]:
        """Assign timeframes to files"""
        timeframe_files = {}
        file_list = list(usable_files.keys())
        timeframe_names = ['1min', '5min', '15min', '30min', '60min']
        
        print(f"\n📊 ASSIGNING TIMEFRAMES")
        print("=" * 50)
        
        for i, file in enumerate(file_list[:5]):  # Use up to 5 files
            timeframe = timeframe_names[i] if i < len(timeframe_names) else f'tf{i+1}'
            timeframe_files[timeframe] = file
            indicators = usable_files[file]['indicators']
            print(f"📈 {timeframe}: {file} ({indicators} indicators)")
        
        return timeframe_files
    
    def load_timeframe_data(self, timeframe_files: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load data from timeframe files"""
        timeframe_data = {}
        
        print(f"\n📂 LOADING TIMEFRAME DATA")
        print("=" * 50)
        
        for timeframe, filename in timeframe_files.items():
            try:
                filepath = os.path.join(self.current_dir, filename)
                df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                
                # Transpose if needed (indicators as columns, time as rows)
                if len(df.columns) > len(df):
                    df_transposed = df.set_index(df.columns[0]).T
                else:
                    first_col = df.iloc[:, 0]
                    if any(':' in str(val) for val in first_col.head(10)):
                        df_transposed = df.set_index(df.columns[0]).T
                    else:
                        df_transposed = df
                
                # Clean data
                for col in df_transposed.columns:
                    df_transposed[col] = pd.to_numeric(df_transposed[col], errors='coerce')
                
                timeframe_data[timeframe] = df_transposed
                print(f"✅ {timeframe}: {df_transposed.shape}")
                
            except Exception as e:
                print(f"❌ Error loading {timeframe}: {str(e)}")
        
        return timeframe_data
    
    def analyze_confluence(self, timeframe_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze confluence across timeframes"""
        print(f"\n🔍 ANALYZING CONFLUENCE")
        print("=" * 50)
        
        # Key indicators to analyze
        key_indicators = [
            'SQUEEZE_SQZ_OFF', 'SQUEEZE_PRO_SQZPRO_OFF',
            'RSI_14', 'MACD_12_26_9_MACD_12_26_9', 'ADX_14_ADX_14',
            'SUPERTREND_7_3.0_SUPERTd_7_3.0', 'BBANDS_5_2_BBB_5_2.0',
            'CCI_14', 'MFI_14', 'WILLR_14', 'STOCH_14_3_STOCHk_14_3_3',
            'VORTEX_14_VTXP_14', 'ACCBANDS_10_ACCBU_10'
        ]
        
        confluence_results = {}
        
        for indicator in key_indicators:
            confluence_info = {
                'timeframes_with_signal': [],
                'signal_strength': {},
                'confluence_score': 0.0,
                'latest_values': {}
            }
            
            total_timeframes = 0
            timeframes_with_signal = 0
            
            for timeframe, df in timeframe_data.items():
                if indicator in df.columns:
                    total_timeframes += 1
                    values = df[indicator].dropna()
                    
                    if len(values) > 0:
                        latest_value = values.iloc[-1]
                        confluence_info['latest_values'][timeframe] = latest_value
                        
                        # Determine if there's a signal
                        has_signal = False
                        signal_strength = 0.0
                        
                        if 'SQUEEZE' in indicator and 'OFF' in indicator:
                            has_signal = latest_value == 1
                            signal_strength = 1.0 if has_signal else 0.0
                        elif 'RSI' in indicator:
                            if latest_value > 70 or latest_value < 30:
                                has_signal = True
                                signal_strength = abs(latest_value - 50) / 50
                        elif 'MACD' in indicator:
                            if len(values) > 1 and abs(latest_value) > values.std():
                                has_signal = True
                                signal_strength = min(abs(latest_value) / values.std(), 1.0)
                        elif 'ADX' in indicator:
                            if latest_value > 25:
                                has_signal = True
                                signal_strength = min(latest_value / 50, 1.0)
                        elif 'CCI' in indicator:
                            if abs(latest_value) > 100:
                                has_signal = True
                                signal_strength = min(abs(latest_value) / 200, 1.0)
                        elif 'MFI' in indicator:
                            if latest_value > 80 or latest_value < 20:
                                has_signal = True
                                signal_strength = abs(latest_value - 50) / 50
                        elif 'WILLR' in indicator:
                            if latest_value > -20 or latest_value < -80:
                                has_signal = True
                                signal_strength = abs(latest_value + 50) / 50
                        elif 'STOCH' in indicator:
                            if latest_value > 80 or latest_value < 20:
                                has_signal = True
                                signal_strength = abs(latest_value - 50) / 50
                        elif 'VORTEX' in indicator:
                            if latest_value > 1.1 or latest_value < 0.9:
                                has_signal = True
                                signal_strength = abs(latest_value - 1.0)
                        elif 'ACCBANDS' in indicator:
                            if len(values) > 1:
                                recent_change = abs(latest_value - values.iloc[-2])
                                avg_change = values.diff().abs().mean()
                                if recent_change > avg_change * 1.5:
                                    has_signal = True
                                    signal_strength = min(recent_change / (avg_change * 2), 1.0)
                        
                        if has_signal:
                            timeframes_with_signal += 1
                            confluence_info['timeframes_with_signal'].append(timeframe)
                            confluence_info['signal_strength'][timeframe] = signal_strength
            
            # Calculate confluence score
            if total_timeframes > 0:
                confluence_info['confluence_score'] = timeframes_with_signal / total_timeframes
                confluence_results[indicator] = confluence_info
        
        return confluence_results
    
    def generate_recommendations(self, confluence_results: Dict[str, Any], timeframe_data: Dict[str, pd.DataFrame]) -> List[str]:
        """Generate trading recommendations"""
        recommendations = []
        
        # Calculate overall confluence
        scores = [info['confluence_score'] for info in confluence_results.values()]
        overall_score = np.mean(scores) if scores else 0.0
        
        recommendations.append("🎯 MULTI-TIMEFRAME TRADING RECOMMENDATIONS")
        recommendations.append("=" * 50)
        
        if overall_score > 0.6:
            recommendations.append("🟢 HIGH CONFLUENCE - Strong multi-timeframe alignment")
            recommendations.append("   • High probability setup detected")
            recommendations.append("   • Consider larger position size")
            recommendations.append("   • Multiple timeframes confirm signal")
        elif overall_score > 0.3:
            recommendations.append("🟡 MODERATE CONFLUENCE - Some timeframe alignment")
            recommendations.append("   • Moderate probability setup")
            recommendations.append("   • Use standard position size")
            recommendations.append("   • Wait for additional confirmation")
        else:
            recommendations.append("🔴 LOW CONFLUENCE - Limited timeframe alignment")
            recommendations.append("   • Low probability setup")
            recommendations.append("   • Avoid trading or use small size")
            recommendations.append("   • Wait for better setup")
        
        recommendations.append(f"\n📊 Overall Confluence Score: {overall_score:.2f}")
        
        # Add specific indicator insights
        recommendations.append("\n🔍 KEY INDICATOR SIGNALS:")
        
        # Sort by confluence score
        sorted_indicators = sorted(confluence_results.items(), 
                                 key=lambda x: x[1]['confluence_score'], 
                                 reverse=True)
        
        for indicator, info in sorted_indicators[:10]:  # Top 10
            score = info['confluence_score']
            timeframes = ', '.join(info['timeframes_with_signal'])
            if score > 0:
                recommendations.append(f"   • {indicator}: {score:.2f} ({timeframes})")
        
        # Add timeframe-specific guidance
        recommendations.append("\n📈 TIMEFRAME-SPECIFIC GUIDANCE:")
        timeframes = list(timeframe_data.keys())
        
        if '1min' in timeframes:
            recommendations.append("   • 1-minute: Use for precise entry/exit timing")
        if '5min' in timeframes:
            recommendations.append("   • 5-minute: Primary signal confirmation")
        if '15min' in timeframes:
            recommendations.append("   • 15-minute: Trend direction validation")
        if '30min' in timeframes:
            recommendations.append("   • 30-minute: Major level identification")
        if '60min' in timeframes:
            recommendations.append("   • 60-minute: Overall market structure")
        
        return recommendations
    
    def run_analysis(self) -> Dict[str, Any]:
        """Run complete multi-timeframe analysis"""
        print("\n🚀 STARTING SIMPLE MULTI-TIMEFRAME ANALYSIS")
        print("=" * 80)
        
        # Find usable files
        usable_files = self.find_usable_files()
        
        if len(usable_files) < 2:
            print("❌ Need at least 2 usable files for analysis")
            return {}
        
        # Assign timeframes
        timeframe_files = self.assign_timeframes(usable_files)
        
        # Load data
        timeframe_data = self.load_timeframe_data(timeframe_files)
        
        if len(timeframe_data) < 2:
            print("❌ Need at least 2 timeframes loaded for analysis")
            return {}
        
        # Analyze confluence
        confluence_results = self.analyze_confluence(timeframe_data)
        
        # Generate recommendations
        recommendations = self.generate_recommendations(confluence_results, timeframe_data)
        
        # Print recommendations
        print("\n")
        for rec in recommendations:
            print(rec)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results = {
            'timeframes_analyzed': list(timeframe_data.keys()),
            'files_used': timeframe_files,
            'confluence_analysis': confluence_results,
            'recommendations': recommendations,
            'timestamp': timestamp
        }
        
        # Save to file
        results_filename = f"simple_multi_timeframe_analysis_{timestamp}.json"
        with open(os.path.join(self.current_dir, results_filename), 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {results_filename}")
        
        return results


def main():
    """
    Main execution function
    """
    print("🚀 Simple Multi-Timeframe Analyzer")
    print("=" * 60)
    
    # Initialize and run analyzer
    analyzer = SimpleMultiTimeframeAnalyzer()
    results = analyzer.run_analysis()
    
    if results:
        print("\n✅ Multi-timeframe analysis completed successfully!")
        print("📊 Check the generated file for detailed results.")
    else:
        print("\n❌ Multi-timeframe analysis failed.")


if __name__ == "__main__":
    main()
