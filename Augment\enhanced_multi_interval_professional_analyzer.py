"""
Enhanced Multi-Interval Professional Analyzer
- Generates separate files for each interval using integrated_technical_analyzer
- Asks for user inputs (ticker, exchange, date, time, intervals)
- Runs separate commands for each interval
- Renames files immediately after each command
- Uses proper interval-specific data for advanced professional analysis
"""

import subprocess
import os
import time
import glob
import shutil
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any

class EnhancedMultiIntervalProfessionalAnalyzer:
    def __init__(self):
        print("🚀 Enhanced Multi-Interval Professional Analyzer initialized")
        print("📊 Separate file generation for each interval")
        print("⏰ Individual command execution for each timeframe")
        print("🎯 Professional signal analysis with proper interval data")
        
        # Available intervals
        self.available_intervals = ["1", "3", "5", "10", "15", "30", "60", "120", "240"]
        
    def get_user_inputs(self) -> Dict[str, Any]:
        """Get comprehensive user inputs"""
        
        print(f"\n📝 ENTER ANALYSIS PARAMETERS")
        print("=" * 60)
        
        # Asset details
        ticker = input("📊 Enter ticker (e.g., NATURALGAS26AUG25): ").strip().upper()
        
        print(f"\n🏢 Available exchanges: NSE, BSE, MCX, NFO")
        exchange = input("🏢 Enter exchange: ").strip().upper()
        
        date = input("📅 Enter date (DD-MM-YYYY): ").strip()
        
        # Time range
        print(f"\n⏰ TIME RANGE SELECTION")
        print("=" * 30)
        start_time = input("🕐 Enter start time (HH:MM, e.g., 10:00): ").strip()
        end_time = input("🕕 Enter end time (HH:MM, e.g., 22:32): ").strip()
        
        # Intervals selection
        print(f"\n📈 INTERVAL SELECTION")
        print("=" * 30)
        print(f"Available intervals: {', '.join(self.available_intervals)} minutes")
        intervals_input = input("Enter intervals (comma-separated, e.g., 1,5,15,30): ").strip()
        intervals = [interval.strip() for interval in intervals_input.split(',')]
        
        # Validate intervals
        valid_intervals = []
        for interval in intervals:
            if interval in self.available_intervals:
                valid_intervals.append(interval)
            else:
                print(f"⚠️  Invalid interval '{interval}' - skipping")
        
        if not valid_intervals:
            print("❌ No valid intervals provided")
            return {}
        
        # Analysis options
        print(f"\n🔧 ANALYSIS OPTIONS")
        print("=" * 30)
        run_advanced_analysis = input("🎯 Run advanced professional signal analysis? (y/n): ").strip().lower() == 'y'
        
        return {
            'ticker': ticker,
            'exchange': exchange,
            'date': date,
            'start_time': start_time,
            'end_time': end_time,
            'intervals': valid_intervals,
            'run_advanced_analysis': run_advanced_analysis
        }
    
    def generate_interval_data(self, inputs: Dict[str, Any]) -> Dict[str, str]:
        """Generate separate data files for each interval"""
        
        print(f"\n🔄 GENERATING SEPARATE FILES FOR EACH INTERVAL")
        print("=" * 80)
        print(f"📊 Ticker: {inputs['ticker']}")
        print(f"🏢 Exchange: {inputs['exchange']}")
        print(f"📅 Date: {inputs['date']}")
        print(f"⏰ Time: {inputs['start_time']} - {inputs['end_time']}")
        print(f"📈 Intervals: {', '.join(inputs['intervals'])} minutes")
        
        interval_files = {}
        
        for i, interval in enumerate(inputs['intervals'], 1):
            print(f"\n🔄 STEP {i}/{len(inputs['intervals'])}: Generating {interval}-minute data")
            print("=" * 60)
            
            # Build command for this interval
            cmd = [
                'python', 'integrated_technical_analyzer.py',
                '--mode', 'analysis',
                '--analysis-type', 'signals',
                '--ticker', inputs['ticker'],
                '--exchange', inputs['exchange'],
                '--date', inputs['date'],
                '--method', 'strategy_all',
                '--interval', interval,
                '--start-time', inputs['start_time'],
                '--end-time', inputs['end_time']
            ]
            
            print(f"📊 Interval: {interval} minutes")
            print(f"🔧 Command: {' '.join(cmd)}")
            
            try:
                # Run the command
                print(f"⏳ Executing command for {interval}-minute interval...")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
                
                if result.returncode == 0:
                    print(f"✅ {interval}-minute data generation completed!")
                    
                    # Find the generated file
                    pattern = f"technical_analysis_{inputs['ticker']}_{inputs['exchange']}_signals_*.xlsx"
                    files = glob.glob(pattern)
                    
                    if files:
                        # Get the most recent file
                        latest_file = max(files, key=os.path.getctime)
                        
                        # Create new filename with interval
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        new_filename = f"technical_analysis_{inputs['ticker']}_{inputs['exchange']}_signals_{interval}min_{timestamp}.xlsx"
                        
                        # Rename the file
                        shutil.move(latest_file, new_filename)
                        interval_files[f"{interval}min"] = new_filename
                        
                        print(f"📄 File renamed to: {new_filename}")
                        
                        # Show file info
                        file_size = os.path.getsize(new_filename) / (1024 * 1024)  # MB
                        print(f"📊 File size: {file_size:.2f} MB")
                        
                    else:
                        print(f"❌ No output file found for {interval}-minute interval")
                        
                else:
                    print(f"❌ {interval}-minute data generation failed!")
                    print(f"Error: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"❌ {interval}-minute data generation timed out after 10 minutes")
            except Exception as e:
                print(f"❌ Error generating {interval}-minute data: {str(e)}")
            
            # Wait between commands to avoid conflicts
            if i < len(inputs['intervals']):
                print(f"⏳ Waiting 2 seconds before next interval...")
                time.sleep(2)
        
        print(f"\n📊 INTERVAL DATA GENERATION SUMMARY")
        print("=" * 60)
        print(f"✅ Successfully generated: {len(interval_files)} files")
        for interval, filename in interval_files.items():
            print(f"   📄 {interval}: {filename}")
        
        if len(interval_files) != len(inputs['intervals']):
            failed_intervals = set(f"{i}min" for i in inputs['intervals']) - set(interval_files.keys())
            print(f"❌ Failed intervals: {', '.join(failed_intervals)}")
        
        return interval_files
    
    def run_advanced_professional_analysis(self, inputs: Dict[str, Any],
                                         interval_files: Dict[str, str]) -> bool:
        """Run advanced ML-enhanced professional signal analysis with proper interval files"""

        print(f"\n🎯 ADVANCED ML-ENHANCED PROFESSIONAL SIGNAL ANALYSIS")
        print("=" * 80)
        print("⏰ Real time format (not Period_1, Period_2)")
        print("🎯 Proper stop loss and targets for PGO")
        print("🤖 ML-based threshold optimization for near 1.0 accuracy")
        print("🔄 Separate thresholds for reversals vs breakouts")
        print("📊 Advanced overbought/oversold detection")

        try:
            from advanced_ml_enhanced_professional_analyzer import AdvancedMLEnhancedProfessionalAnalyzer

            analyzer = AdvancedMLEnhancedProfessionalAnalyzer()

            # Load data from separate interval files
            timeframe_data = self._load_interval_specific_data(interval_files)

            if len(timeframe_data) < 2:
                print("❌ Need at least 2 intervals for analysis")
                return False

            # Define indicators to analyze
            indicators = ['PGO_14', 'CCI_14', 'SMI_5_20_5_SMIo_5_20_5_100.0', 'BIAS_26', 'CG_10']

            # Filter available indicators
            available_indicators = self._filter_available_indicators(indicators, timeframe_data)

            if not available_indicators:
                print("❌ No configured indicators found")
                return False

            print(f"\n📊 Analyzing {len(available_indicators)} professional indicators:")
            for indicator in available_indicators:
                print(f"   ✅ {indicator}")

            # Perform advanced ML-enhanced signal detection
            print(f"\n🤖 PERFORMING ML-ENHANCED SIGNAL DETECTION...")
            signal_results = analyzer.detect_advanced_signals_with_real_time(timeframe_data, available_indicators)

            # Export to Excel with enhanced format
            excel_filename = self._export_ml_enhanced_analysis_to_excel(signal_results, inputs)

            # Print enhanced summary
            self._print_ml_enhanced_summary(signal_results)

            print(f"\n💾 ML-Enhanced analysis results saved to: {excel_filename}")

            return True

        except Exception as e:
            print(f"❌ Error running ML-enhanced professional analysis: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def _load_interval_specific_data(self, interval_files: Dict[str, str]) -> Dict[str, Any]:
        """Load data from interval-specific files"""
        
        print(f"\n📂 LOADING INTERVAL-SPECIFIC DATA")
        print("=" * 60)
        
        timeframe_data = {}
        
        for interval, filename in interval_files.items():
            try:
                import pandas as pd
                
                # Load the Excel file
                excel_file = pd.ExcelFile(filename)
                
                # Look for time-series data sheet
                time_series_sheet = None
                for sheet_name in excel_file.sheet_names:
                    if 'time' in sheet_name.lower() or 'series' in sheet_name.lower():
                        time_series_sheet = sheet_name
                        break
                
                if not time_series_sheet:
                    time_series_sheet = excel_file.sheet_names[0]
                
                # Load the data
                df = pd.read_excel(filename, sheet_name=time_series_sheet)
                
                # Transpose data from indicator rows to indicator columns
                if 'Indicator' in df.columns:
                    # Get time columns
                    time_columns = [col for col in df.columns if col not in ['Indicator', 'Category']]
                    
                    # Create indicator data dictionary
                    indicator_data = {}
                    for idx, row in df.iterrows():
                        indicator_name = row['Indicator']
                        if indicator_name and indicator_name != '--- TECHNICAL INDICATORS ---':
                            values = []
                            for time_col in time_columns:
                                val = row[time_col]
                                if pd.isna(val):
                                    values.append(float('nan'))
                                else:
                                    try:
                                        values.append(float(val))
                                    except:
                                        values.append(float('nan'))
                            indicator_data[indicator_name] = values
                    
                    # Create DataFrame with proper structure
                    transposed_df = pd.DataFrame(indicator_data, index=time_columns)
                    timeframe_data[interval] = transposed_df
                    
                    print(f"✅ {interval}: {transposed_df.shape} (from {filename})")
                else:
                    timeframe_data[interval] = df
                    print(f"✅ {interval}: {df.shape} (from {filename})")
                
            except Exception as e:
                print(f"❌ Error loading {interval} data from {filename}: {str(e)}")
        
        return timeframe_data

    def _filter_available_indicators(self, indicators: List[str], timeframe_data: Dict[str, Any]) -> List[str]:
        """Filter indicators that are available in the data"""
        available_indicators = []

        # Check first timeframe for available indicators
        first_timeframe = list(timeframe_data.values())[0]

        for indicator in indicators:
            if indicator in first_timeframe.columns:
                available_indicators.append(indicator)
            else:
                print(f"   ⚠️  {indicator} not found in data")

        return available_indicators

    def _export_ml_enhanced_analysis_to_excel(self, signal_results: Dict[str, Any],
                                            inputs: Dict[str, Any]) -> str:
        """Export ML-enhanced analysis results to Excel"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ml_enhanced_analysis_{inputs['ticker']}_{inputs['exchange']}_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # Sheet 1: All Signals with Real Time
                signals_df = pd.DataFrame(signal_results['signals'])
                if not signals_df.empty:
                    signals_df = signals_df.sort_values(['generation_time', 'timeframe'])
                signals_df.to_excel(writer, sheet_name='ML_Enhanced_Signals', index=False)

                # Sheet 2: Reversal Signals Only
                reversal_signals = [s for s in signal_results['signals'] if s['strategy'] == 'REVERSAL']
                if reversal_signals:
                    reversal_df = pd.DataFrame(reversal_signals)
                    reversal_df.to_excel(writer, sheet_name='Reversal_Signals', index=False)

                # Sheet 3: Breakout Signals Only
                breakout_signals = [s for s in signal_results['signals'] if s['strategy'] == 'BREAKOUT']
                if breakout_signals:
                    breakout_df = pd.DataFrame(breakout_signals)
                    breakout_df.to_excel(writer, sheet_name='Breakout_Signals', index=False)

                # Sheet 4: ML Optimization Results
                ml_results = []
                for key, result in signal_results['ml_optimization_results'].items():
                    if isinstance(result, dict):
                        for strategy, strategy_result in result.items():
                            if isinstance(strategy_result, dict) and 'accuracy' in strategy_result:
                                ml_results.append({
                                    'Indicator_Timeframe': key,
                                    'Strategy': strategy,
                                    'ML_Accuracy': strategy_result.get('accuracy', 0),
                                    'ML_Precision': strategy_result.get('precision', 0),
                                    'ML_Recall': strategy_result.get('recall', 0),
                                    'Feature_Importance': strategy_result.get('feature_importance', 0),
                                    'Optimized_Oversold': strategy_result.get('optimized_thresholds', {}).get('oversold', 0),
                                    'Optimized_Overbought': strategy_result.get('optimized_thresholds', {}).get('overbought', 0),
                                    'Optimized_Oversold_Exit': strategy_result.get('optimized_thresholds', {}).get('oversold_exit', 0),
                                    'Optimized_Overbought_Exit': strategy_result.get('optimized_thresholds', {}).get('overbought_exit', 0)
                                })

                if ml_results:
                    ml_df = pd.DataFrame(ml_results)
                    ml_df.to_excel(writer, sheet_name='ML_Optimization_Results', index=False)

                # Sheet 5: Summary Statistics
                summary_data = {
                    'Metric': ['Total Signals', 'Reversal Signals', 'Breakout Signals', 'Buy Signals', 'Sell Signals'],
                    'Count': [
                        signal_results['total_signals'],
                        len(reversal_signals),
                        len(breakout_signals),
                        len([s for s in signal_results['signals'] if s['signal_type'] == 'BUY']),
                        len([s for s in signal_results['signals'] if s['signal_type'] == 'SELL'])
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

            print(f"✅ Excel file created: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error creating Excel file: {str(e)}")
            return ""

    def _print_ml_enhanced_summary(self, signal_results: Dict[str, Any]):
        """Print enhanced summary of ML results"""

        print(f"\n📊 ML-ENHANCED ANALYSIS SUMMARY")
        print("=" * 80)

        signals = signal_results['signals']

        print(f"🎯 SIGNAL SUMMARY:")
        print(f"   📊 Total Signals: {len(signals)}")
        print(f"   🔄 Reversal Signals: {len([s for s in signals if s['strategy'] == 'REVERSAL'])}")
        print(f"   📈 Breakout Signals: {len([s for s in signals if s['strategy'] == 'BREAKOUT'])}")
        print(f"   🟢 Buy Signals: {len([s for s in signals if s['signal_type'] == 'BUY'])}")
        print(f"   🔴 Sell Signals: {len([s for s in signals if s['signal_type'] == 'SELL'])}")

        # Show top signals with real time
        print(f"\n🏆 TOP SIGNALS WITH REAL TIME FORMAT:")

        # Sort by strength and show top 10
        sorted_signals = sorted(signals, key=lambda x: x.get('strength', 0), reverse=True)[:10]

        for i, signal in enumerate(sorted_signals, 1):
            print(f"\n   {i:2d}. {signal['indicator']} - {signal['signal_type']} ({signal['strategy']})")
            print(f"       ⏰ Generation: {signal['generation_time']}")
            print(f"       ✅ Confirmation: {signal['confirmation_time']}")
            print(f"       🎯 Entry: {signal['entry_time']}")
            print(f"       💰 Price: {signal['current_price']}")
            print(f"       🛑 Stop Loss: {signal['stop_loss']} ({signal['stop_loss_pct']}%)")
            print(f"       🎯 Target: {signal['target']} ({signal['target_pct']}%)")
            print(f"       📊 Strength: {signal['strength']:.3f}")
            print(f"       📝 Reason: {signal['reason']}")

        # Show ML optimization results
        print(f"\n🤖 ML OPTIMIZATION RESULTS:")
        for key, result in signal_results['ml_optimization_results'].items():
            if isinstance(result, dict):
                print(f"\n   📊 {key}:")
                for strategy, strategy_result in result.items():
                    if isinstance(strategy_result, dict) and 'accuracy' in strategy_result:
                        accuracy = strategy_result.get('accuracy', 0)
                        precision = strategy_result.get('precision', 0)
                        thresholds = strategy_result.get('optimized_thresholds', {})

                        print(f"      🔄 {strategy.upper()}:")
                        print(f"         🎯 ML Accuracy: {accuracy:.3f}")
                        print(f"         📊 ML Precision: {precision:.3f}")
                        print(f"         📉 Optimized Oversold: {thresholds.get('oversold', 0):.2f}")
                        print(f"         📈 Optimized Overbought: {thresholds.get('overbought', 0):.2f}")

    def run_complete_workflow(self) -> bool:
        """Run the complete enhanced multi-interval workflow"""
        
        print("🚀 ENHANCED MULTI-INTERVAL PROFESSIONAL ANALYZER")
        print("=" * 80)
        print("📊 Step 1: Get user inputs")
        print("🔄 Step 2: Generate separate files for each interval")
        print("🎯 Step 3: Advanced professional signal analysis")
        print("📄 Step 4: Comprehensive Excel reports")
        
        # Step 1: Get user inputs
        inputs = self.get_user_inputs()
        
        if not inputs:
            print("❌ Invalid inputs provided")
            return False
        
        # Step 2: Generate interval data
        interval_files = self.generate_interval_data(inputs)
        
        if not interval_files:
            print("❌ No interval files generated")
            return False
        
        # Step 3: Advanced analysis (if requested)
        if inputs['run_advanced_analysis']:
            success = self.run_advanced_professional_analysis(inputs, interval_files)
            
            if success:
                print(f"\n✅ ENHANCED MULTI-INTERVAL WORKFLOW COMPLETED!")
                print("=" * 80)
                print("🎯 WORKFLOW SUMMARY:")
                print(f"   📊 Step 1: ✅ User inputs collected")
                print(f"   🔄 Step 2: ✅ {len(interval_files)} separate interval files generated")
                print(f"   🎯 Step 3: ✅ Advanced professional signal analysis completed")
                print(f"   📄 Step 4: ✅ Comprehensive Excel reports generated")
                
                print(f"\n🏆 KEY ACHIEVEMENTS:")
                print("   📈 Separate files for each interval (no sampling)")
                print("   ⏰ Proper interval-specific data for each timeframe")
                print("   🔄 PGO-style reversal detection with real data")
                print("   📊 Data-driven threshold optimization per interval")
                print("   🎯 Professional signal analysis with timing")
                
                return True
            else:
                print(f"\n⚠️  Interval files generated but advanced analysis failed")
                return False
        else:
            print(f"\n✅ INTERVAL DATA GENERATION COMPLETED!")
            print("=" * 60)
            print(f"📊 Generated {len(interval_files)} separate interval files")
            print("💡 Run advanced analysis separately if needed")
            return True


def main():
    """Main execution function"""
    
    analyzer = EnhancedMultiIntervalProfessionalAnalyzer()
    success = analyzer.run_complete_workflow()
    
    if success:
        print(f"\n🎉 SUCCESS! Enhanced Multi-Interval Analysis completed!")
    else:
        print(f"\n❌ Analysis failed.")


if __name__ == "__main__":
    main()
