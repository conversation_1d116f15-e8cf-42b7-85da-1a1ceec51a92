"""
Individual Indicator Multi-Timeframe Analyzer

Analyzes each indicator separately across timeframes:
- Individual indicator signals (not combined)
- Higher timeframe to lower timeframe hierarchy
- Overbought/Oversold detection for each indicator
- Breakout pattern detection for each indicator
- Professional thresholds that vary by timeframe
- Signal validation with actual price movement
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

class IndividualIndicatorMultiTimeframeAnalyzer:
    """
    Analyze each indicator individually across multiple timeframes
    """
    
    def __init__(self):
        """Initialize the analyzer"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Professional thresholds that vary by timeframe for each indicator
        self.timeframe_thresholds = {
            # RSI - Different thresholds for different timeframes
            'RSI_14': {
                '1min': {'overbought': 75, 'oversold': 25, 'breakout_high': 80, 'breakout_low': 20},
                '5min': {'overbought': 70, 'oversold': 30, 'breakout_high': 75, 'breakout_low': 25},
                '15min': {'overbought': 65, 'oversold': 35, 'breakout_high': 70, 'breakout_low': 30},
                '30min': {'overbought': 60, 'oversold': 40, 'breakout_high': 65, 'breakout_low': 35},
                '60min': {'overbought': 55, 'oversold': 45, 'breakout_high': 60, 'breakout_low': 40}
            },
            
            # MACD - Different thresholds for different timeframes
            'MACD_12_26_9_MACD_12_26_9': {
                '1min': {'bullish': 0.01, 'bearish': -0.01, 'breakout_high': 0.05, 'breakout_low': -0.05},
                '5min': {'bullish': 0.05, 'bearish': -0.05, 'breakout_high': 0.15, 'breakout_low': -0.15},
                '15min': {'bullish': 0.1, 'bearish': -0.1, 'breakout_high': 0.3, 'breakout_low': -0.3},
                '30min': {'bullish': 0.2, 'bearish': -0.2, 'breakout_high': 0.5, 'breakout_low': -0.5},
                '60min': {'bullish': 0.3, 'bearish': -0.3, 'breakout_high': 0.8, 'breakout_low': -0.8}
            },
            
            # ADX - Trend strength varies by timeframe
            'ADX_14_ADX_14': {
                '1min': {'weak': 15, 'moderate': 25, 'strong': 35, 'very_strong': 50},
                '5min': {'weak': 20, 'moderate': 30, 'strong': 40, 'very_strong': 55},
                '15min': {'weak': 25, 'moderate': 35, 'strong': 45, 'very_strong': 60},
                '30min': {'weak': 30, 'moderate': 40, 'strong': 50, 'very_strong': 65},
                '60min': {'weak': 35, 'moderate': 45, 'strong': 55, 'very_strong': 70}
            },
            
            # CCI - Commodity Channel Index
            'CCI_14': {
                '1min': {'overbought': 120, 'oversold': -120, 'breakout_high': 150, 'breakout_low': -150},
                '5min': {'overbought': 110, 'oversold': -110, 'breakout_high': 140, 'breakout_low': -140},
                '15min': {'overbought': 100, 'oversold': -100, 'breakout_high': 130, 'breakout_low': -130},
                '30min': {'overbought': 90, 'oversold': -90, 'breakout_high': 120, 'breakout_low': -120},
                '60min': {'overbought': 80, 'oversold': -80, 'breakout_high': 110, 'breakout_low': -110}
            },
            
            # Stochastic
            'STOCH_14_3_STOCHk_14_3_3': {
                '1min': {'overbought': 85, 'oversold': 15, 'breakout_high': 90, 'breakout_low': 10},
                '5min': {'overbought': 80, 'oversold': 20, 'breakout_high': 85, 'breakout_low': 15},
                '15min': {'overbought': 75, 'oversold': 25, 'breakout_high': 80, 'breakout_low': 20},
                '30min': {'overbought': 70, 'oversold': 30, 'breakout_high': 75, 'breakout_low': 25},
                '60min': {'overbought': 65, 'oversold': 35, 'breakout_high': 70, 'breakout_low': 30}
            },
            
            # Williams %R
            'WILLR_14': {
                '1min': {'overbought': -15, 'oversold': -85, 'breakout_high': -10, 'breakout_low': -90},
                '5min': {'overbought': -20, 'oversold': -80, 'breakout_high': -15, 'breakout_low': -85},
                '15min': {'overbought': -25, 'oversold': -75, 'breakout_high': -20, 'breakout_low': -80},
                '30min': {'overbought': -30, 'oversold': -70, 'breakout_high': -25, 'breakout_low': -75},
                '60min': {'overbought': -35, 'oversold': -65, 'breakout_high': -30, 'breakout_low': -70}
            },
            
            # MFI - Money Flow Index
            'MFI_14': {
                '1min': {'overbought': 85, 'oversold': 15, 'breakout_high': 90, 'breakout_low': 10},
                '5min': {'overbought': 80, 'oversold': 20, 'breakout_high': 85, 'breakout_low': 15},
                '15min': {'overbought': 75, 'oversold': 25, 'breakout_high': 80, 'breakout_low': 20},
                '30min': {'overbought': 70, 'oversold': 30, 'breakout_high': 75, 'breakout_low': 25},
                '60min': {'overbought': 65, 'oversold': 35, 'breakout_high': 70, 'breakout_low': 30}
            },
            
            # Bollinger Band Position
            'BBANDS_5_2_BBP_5_2.0': {
                '1min': {'overbought': 0.9, 'oversold': 0.1, 'breakout_high': 0.95, 'breakout_low': 0.05},
                '5min': {'overbought': 0.85, 'oversold': 0.15, 'breakout_high': 0.9, 'breakout_low': 0.1},
                '15min': {'overbought': 0.8, 'oversold': 0.2, 'breakout_high': 0.85, 'breakout_low': 0.15},
                '30min': {'overbought': 0.75, 'oversold': 0.25, 'breakout_high': 0.8, 'breakout_low': 0.2},
                '60min': {'overbought': 0.7, 'oversold': 0.3, 'breakout_high': 0.75, 'breakout_low': 0.25}
            },
            
            # Squeeze Indicators (Binary - same across timeframes)
            'SQUEEZE_SQZ_OFF': {
                '1min': {'breakout': 1}, '5min': {'breakout': 1}, '15min': {'breakout': 1},
                '30min': {'breakout': 1}, '60min': {'breakout': 1}
            },
            'SQUEEZE_PRO_SQZPRO_OFF': {
                '1min': {'breakout': 1}, '5min': {'breakout': 1}, '15min': {'breakout': 1},
                '30min': {'breakout': 1}, '60min': {'breakout': 1}
            }
        }
        
        print("🚀 Individual Indicator Multi-Timeframe Analyzer initialized")
        print(f"📊 Configured thresholds for {len(self.timeframe_thresholds)} indicators")
    
    def get_user_inputs(self) -> Dict[str, Any]:
        """Get all inputs from user via CLI"""
        print("\n📝 INDIVIDUAL INDICATOR ANALYSIS - INPUT COLLECTION")
        print("=" * 60)
        
        inputs = {}
        
        # Ticker input
        inputs['ticker'] = input("📊 Enter ticker symbol (e.g., NATURALGAS26AUG25): ").strip().upper()
        
        # Exchange input
        print("\n🏢 Available exchanges: NSE, BSE, MCX, NFO")
        inputs['exchange'] = input("🏢 Enter exchange: ").strip().upper()
        
        # Date input
        inputs['date'] = input("📅 Enter date (DD-MM-YYYY format): ").strip()
        
        # Timeframes input
        print("\n⏰ Available timeframes: 1, 5, 15, 30, 60, 120, 240 minutes")
        timeframes_input = input("⏰ Enter timeframes (comma-separated, e.g., 1,5,15,30): ").strip()
        inputs['timeframes'] = [tf.strip() for tf in timeframes_input.split(',')]
        
        # Indicators input
        print("\n🔍 Individual indicator selection:")
        print("   • Enter 'ALL' for all configured indicators")
        print("   • Enter specific indicators (comma-separated)")
        print("   • Available: RSI_14, MACD_12_26_9_MACD_12_26_9, ADX_14_ADX_14, CCI_14, etc.")
        indicators_input = input("🔍 Enter indicators: ").strip().upper()
        
        if indicators_input == 'ALL':
            inputs['indicators'] = list(self.timeframe_thresholds.keys())
        else:
            inputs['indicators'] = [ind.strip() for ind in indicators_input.split(',')]
        
        # Signal validation
        print("\n✅ Signal validation:")
        validate_signals = input("✅ Validate signals with price movement? (y/n, default=y): ").strip().lower()
        inputs['validate_signals'] = validate_signals != 'n'
        
        return inputs
    
    def find_data_files(self, ticker: str, exchange: str, date: str) -> List[str]:
        """Find data files matching the criteria"""
        print(f"\n🔍 SEARCHING FOR DATA FILES")
        print("=" * 50)
        
        matching_files = []
        
        for file in os.listdir(self.current_dir):
            if (ticker in file and 
                exchange in file and 
                file.endswith('.xlsx') and 
                not file.startswith('~$')):
                
                # Check if file has required sheet
                try:
                    filepath = os.path.join(self.current_dir, file)
                    excel_file = pd.ExcelFile(filepath)
                    
                    if 'Time_Series_Indicators' in excel_file.sheet_names:
                        df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                        if df.shape[1] > 50:  # Should have many indicators
                            matching_files.append(file)
                            print(f"✅ Found: {file} ({df.shape[1]} indicators)")
                except Exception as e:
                    print(f"⚠️ Error checking {file}: {str(e)}")
        
        return matching_files
    
    def assign_timeframes_to_files(self, files: List[str], requested_timeframes: List[str]) -> Dict[str, str]:
        """Assign timeframes to available files"""
        print(f"\n📊 ASSIGNING TIMEFRAMES TO FILES")
        print("=" * 50)
        
        timeframe_files = {}
        
        for i, timeframe in enumerate(requested_timeframes):
            if i < len(files):
                timeframe_files[f"{timeframe}min"] = files[i]
                print(f"📈 {timeframe}min: {files[i]}")
            else:
                print(f"⚠️ No file available for {timeframe}min timeframe")
        
        return timeframe_files
    
    def load_timeframe_data(self, timeframe_files: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load data from timeframe files"""
        print(f"\n📂 LOADING TIMEFRAME DATA")
        print("=" * 50)
        
        timeframe_data = {}
        
        for timeframe, filename in timeframe_files.items():
            try:
                filepath = os.path.join(self.current_dir, filename)
                df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                
                # Transpose if needed (indicators as columns, time as rows)
                if len(df.columns) > len(df):
                    df_transposed = df.set_index(df.columns[0]).T
                else:
                    first_col = df.iloc[:, 0]
                    if any(':' in str(val) for val in first_col.head(10)):
                        df_transposed = df.set_index(df.columns[0]).T
                    else:
                        df_transposed = df
                
                # Clean data
                for col in df_transposed.columns:
                    df_transposed[col] = pd.to_numeric(df_transposed[col], errors='coerce')
                
                timeframe_data[timeframe] = df_transposed
                print(f"✅ {timeframe}: {df_transposed.shape}")
                
            except Exception as e:
                print(f"❌ Error loading {timeframe}: {str(e)}")
        
        return timeframe_data

    def analyze_individual_indicator_signal(self, indicator: str, value: float,
                                          timeframe: str) -> Dict[str, Any]:
        """
        Analyze signal for individual indicator in specific timeframe
        Returns detailed signal analysis including overbought/oversold and breakout detection
        """
        signal_analysis = {
            'indicator': indicator,
            'timeframe': timeframe,
            'value': value,
            'signal_type': 'NEUTRAL',
            'signal_strength': 0.0,
            'condition': 'NORMAL',
            'breakout_detected': False,
            'professional_assessment': ''
        }

        # Get thresholds for this indicator and timeframe
        if indicator not in self.timeframe_thresholds:
            return signal_analysis

        timeframe_config = self.timeframe_thresholds[indicator].get(timeframe, {})
        if not timeframe_config:
            # Use closest timeframe if exact match not found
            available_timeframes = list(self.timeframe_thresholds[indicator].keys())
            if available_timeframes:
                timeframe_config = self.timeframe_thresholds[indicator][available_timeframes[0]]

        if not timeframe_config:
            return signal_analysis

        # Analyze based on indicator type
        if 'RSI' in indicator or 'MFI' in indicator:
            signal_analysis = self._analyze_oscillator_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'STOCH' in indicator:
            signal_analysis = self._analyze_stochastic_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'MACD' in indicator:
            signal_analysis = self._analyze_macd_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'ADX' in indicator:
            signal_analysis = self._analyze_adx_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'CCI' in indicator:
            signal_analysis = self._analyze_cci_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'WILLR' in indicator:
            signal_analysis = self._analyze_williams_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'BBANDS' in indicator and 'BBP' in indicator:
            signal_analysis = self._analyze_bollinger_position_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'SQUEEZE' in indicator and 'OFF' in indicator:
            signal_analysis = self._analyze_squeeze_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        return signal_analysis

    def _analyze_oscillator_signal(self, indicator: str, value: float, timeframe: str,
                                 config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze RSI/MFI type oscillator signals"""

        overbought = config.get('overbought', 70)
        oversold = config.get('oversold', 30)
        breakout_high = config.get('breakout_high', 80)
        breakout_low = config.get('breakout_low', 20)

        # Determine signal type and condition
        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (100 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"Extreme overbought breakout in {timeframe} - Strong sell signal"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
            signal_analysis['professional_assessment'] = f"Extreme oversold breakout in {timeframe} - Strong buy signal"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"Overbought condition in {timeframe} - Consider sell"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"Oversold condition in {timeframe} - Consider buy"

        return signal_analysis

    def _analyze_stochastic_signal(self, indicator: str, value: float, timeframe: str,
                                 config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Stochastic oscillator signals"""

        overbought = config.get('overbought', 80)
        oversold = config.get('oversold', 20)
        breakout_high = config.get('breakout_high', 90)
        breakout_low = config.get('breakout_low', 10)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (100 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"Stochastic extreme overbought in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
            signal_analysis['professional_assessment'] = f"Stochastic extreme oversold in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"Stochastic overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"Stochastic oversold in {timeframe}"

        return signal_analysis

    def _analyze_macd_signal(self, indicator: str, value: float, timeframe: str,
                           config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze MACD signals"""

        bullish = config.get('bullish', 0)
        bearish = config.get('bearish', 0)
        breakout_high = config.get('breakout_high', 0.1)
        breakout_low = config.get('breakout_low', -0.1)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'STRONG_BULLISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value / breakout_high, 1.0)
            signal_analysis['professional_assessment'] = f"MACD strong bullish breakout in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'STRONG_BEARISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(abs(value) / abs(breakout_low), 1.0)
            signal_analysis['professional_assessment'] = f"MACD strong bearish breakout in {timeframe}"

        elif value > bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'BULLISH'
            signal_analysis['signal_strength'] = value / breakout_high
            signal_analysis['professional_assessment'] = f"MACD bullish in {timeframe}"

        elif value < bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'BEARISH'
            signal_analysis['signal_strength'] = abs(value) / abs(breakout_low)
            signal_analysis['professional_assessment'] = f"MACD bearish in {timeframe}"

        return signal_analysis

    def _analyze_adx_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze ADX trend strength signals"""

        weak = config.get('weak', 25)
        moderate = config.get('moderate', 35)
        strong = config.get('strong', 45)
        very_strong = config.get('very_strong', 60)

        if value >= very_strong:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'VERY_STRONG_TREND'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value / 100, 1.0)
            signal_analysis['professional_assessment'] = f"Very strong trend in {timeframe} - Follow trend direction"

        elif value >= strong:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'STRONG_TREND'
            signal_analysis['signal_strength'] = value / very_strong
            signal_analysis['professional_assessment'] = f"Strong trend in {timeframe}"

        elif value >= moderate:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'MODERATE_TREND'
            signal_analysis['signal_strength'] = value / strong
            signal_analysis['professional_assessment'] = f"Moderate trend in {timeframe}"

        elif value >= weak:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'WEAK_TREND'
            signal_analysis['signal_strength'] = value / moderate
            signal_analysis['professional_assessment'] = f"Weak trend in {timeframe}"

        else:
            signal_analysis['condition'] = 'NO_TREND'
            signal_analysis['professional_assessment'] = f"No clear trend in {timeframe}"

        return signal_analysis

    def _analyze_cci_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze CCI signals"""

        overbought = config.get('overbought', 100)
        oversold = config.get('oversold', -100)
        breakout_high = config.get('breakout_high', 150)
        breakout_low = config.get('breakout_low', -150)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (200 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"CCI extreme overbought breakout in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / abs(breakout_low), 1.0)
            signal_analysis['professional_assessment'] = f"CCI extreme oversold breakout in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"CCI overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"CCI oversold in {timeframe}"

        return signal_analysis

    def _analyze_williams_signal(self, indicator: str, value: float, timeframe: str,
                               config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Williams %R signals"""

        overbought = config.get('overbought', -20)
        oversold = config.get('oversold', -80)
        breakout_high = config.get('breakout_high', -10)
        breakout_low = config.get('breakout_low', -90)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (0 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"Williams %R extreme overbought in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / abs(breakout_low), 1.0)
            signal_analysis['professional_assessment'] = f"Williams %R extreme oversold in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"Williams %R overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"Williams %R oversold in {timeframe}"

        return signal_analysis

    def _analyze_bollinger_position_signal(self, indicator: str, value: float, timeframe: str,
                                         config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Bollinger Band Position signals"""

        overbought = config.get('overbought', 0.8)
        oversold = config.get('oversold', 0.2)
        breakout_high = config.get('breakout_high', 0.95)
        breakout_low = config.get('breakout_low', 0.05)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'BAND_BREAKOUT_HIGH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (1.0 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"Bollinger upper band breakout in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'BAND_BREAKOUT_LOW'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
            signal_analysis['professional_assessment'] = f"Bollinger lower band breakout in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'NEAR_UPPER_BAND'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"Near Bollinger upper band in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'NEAR_LOWER_BAND'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"Near Bollinger lower band in {timeframe}"

        return signal_analysis

    def _analyze_squeeze_signal(self, indicator: str, value: float, timeframe: str,
                              config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Squeeze release signals"""

        if value == 1:
            signal_analysis['signal_type'] = 'BREAKOUT'
            signal_analysis['condition'] = 'SQUEEZE_RELEASE'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = 1.0
            signal_analysis['professional_assessment'] = f"Squeeze release breakout in {timeframe} - High probability move"

        return signal_analysis

    def analyze_individual_indicators_across_timeframes(self, timeframe_data: Dict[str, pd.DataFrame],
                                                       indicators: List[str]) -> Dict[str, Any]:
        """
        Analyze each indicator individually across all timeframes
        Higher timeframe to lower timeframe hierarchy
        """
        print(f"\n🎯 INDIVIDUAL INDICATOR ANALYSIS ACROSS TIMEFRAMES")
        print("=" * 60)

        # Sort timeframes by duration (highest to lowest)
        timeframe_order = sorted(timeframe_data.keys(),
                               key=lambda x: int(x.replace('min', '')),
                               reverse=True)

        print(f"📊 Timeframe hierarchy: {' → '.join(timeframe_order)}")

        individual_analysis = {}

        for indicator in indicators:
            print(f"\n🔍 Analyzing {indicator} across timeframes...")

            indicator_analysis = {
                'indicator': indicator,
                'timeframe_signals': {},
                'hierarchy_confirmation': {},
                'overall_assessment': '',
                'trade_recommendation': ''
            }

            # Analyze each timeframe for this indicator
            for timeframe in timeframe_order:
                if indicator in timeframe_data[timeframe].columns:
                    values = timeframe_data[timeframe][indicator].dropna()

                    if len(values) > 0:
                        latest_value = values.iloc[-1]

                        # Get individual signal analysis
                        signal_analysis = self.analyze_individual_indicator_signal(
                            indicator, latest_value, timeframe
                        )

                        indicator_analysis['timeframe_signals'][timeframe] = signal_analysis

                        print(f"   {timeframe}: {signal_analysis['signal_type']} - {signal_analysis['condition']} (Strength: {signal_analysis['signal_strength']:.2f})")

            # Analyze hierarchy confirmation
            indicator_analysis['hierarchy_confirmation'] = self._analyze_hierarchy_confirmation(
                indicator_analysis['timeframe_signals'], timeframe_order
            )

            # Generate overall assessment
            indicator_analysis['overall_assessment'] = self._generate_overall_assessment(
                indicator, indicator_analysis
            )

            # Generate trade recommendation
            indicator_analysis['trade_recommendation'] = self._generate_trade_recommendation(
                indicator, indicator_analysis
            )

            individual_analysis[indicator] = indicator_analysis

        return individual_analysis

    def _analyze_hierarchy_confirmation(self, timeframe_signals: Dict[str, Dict],
                                      timeframe_order: List[str]) -> Dict[str, Any]:
        """Analyze confirmation across timeframe hierarchy"""

        confirmation_analysis = {
            'higher_timeframe_signal': None,
            'lower_timeframe_confirmations': [],
            'conflicting_signals': [],
            'confirmation_strength': 0.0
        }

        # Get highest timeframe signal
        for timeframe in timeframe_order:
            if timeframe in timeframe_signals:
                signal = timeframe_signals[timeframe]
                if signal['signal_type'] != 'NEUTRAL':
                    confirmation_analysis['higher_timeframe_signal'] = {
                        'timeframe': timeframe,
                        'signal': signal
                    }
                    break

        # If no higher timeframe signal, return
        if not confirmation_analysis['higher_timeframe_signal']:
            return confirmation_analysis

        higher_signal_type = confirmation_analysis['higher_timeframe_signal']['signal']['signal_type']

        # Check lower timeframes for confirmation
        confirmations = 0
        conflicts = 0
        total_lower_timeframes = 0

        for timeframe in timeframe_order[1:]:  # Skip highest timeframe
            if timeframe in timeframe_signals:
                total_lower_timeframes += 1
                signal = timeframe_signals[timeframe]

                if signal['signal_type'] == higher_signal_type:
                    confirmations += 1
                    confirmation_analysis['lower_timeframe_confirmations'].append({
                        'timeframe': timeframe,
                        'signal': signal
                    })
                elif signal['signal_type'] != 'NEUTRAL' and signal['signal_type'] != higher_signal_type:
                    conflicts += 1
                    confirmation_analysis['conflicting_signals'].append({
                        'timeframe': timeframe,
                        'signal': signal
                    })

        # Calculate confirmation strength
        if total_lower_timeframes > 0:
            confirmation_analysis['confirmation_strength'] = confirmations / total_lower_timeframes

        return confirmation_analysis

    def _generate_overall_assessment(self, indicator: str, indicator_analysis: Dict) -> str:
        """Generate overall assessment for the indicator"""

        timeframe_signals = indicator_analysis['timeframe_signals']
        hierarchy_confirmation = indicator_analysis['hierarchy_confirmation']

        if not hierarchy_confirmation['higher_timeframe_signal']:
            return f"{indicator}: No clear signal in higher timeframes - NEUTRAL"

        higher_signal = hierarchy_confirmation['higher_timeframe_signal']
        confirmation_strength = hierarchy_confirmation['confirmation_strength']

        signal_type = higher_signal['signal']['signal_type']
        signal_condition = higher_signal['signal']['condition']
        higher_timeframe = higher_signal['timeframe']

        assessment = f"{indicator}: {signal_type} signal from {higher_timeframe} ({signal_condition})"

        if confirmation_strength >= 0.7:
            assessment += f" - STRONG confirmation across {confirmation_strength:.0%} of lower timeframes"
        elif confirmation_strength >= 0.5:
            assessment += f" - MODERATE confirmation across {confirmation_strength:.0%} of lower timeframes"
        else:
            assessment += f" - WEAK confirmation across {confirmation_strength:.0%} of lower timeframes"

        # Add breakout information
        breakout_timeframes = []
        for tf, signal in timeframe_signals.items():
            if signal.get('breakout_detected', False):
                breakout_timeframes.append(tf)

        if breakout_timeframes:
            assessment += f" - BREAKOUT detected in: {', '.join(breakout_timeframes)}"

        return assessment

    def _generate_trade_recommendation(self, indicator: str, indicator_analysis: Dict) -> str:
        """Generate specific trade recommendation for the indicator"""

        hierarchy_confirmation = indicator_analysis['hierarchy_confirmation']

        if not hierarchy_confirmation['higher_timeframe_signal']:
            return "HOLD - No clear directional signal"

        higher_signal = hierarchy_confirmation['higher_timeframe_signal']
        confirmation_strength = hierarchy_confirmation['confirmation_strength']

        signal_type = higher_signal['signal']['signal_type']
        signal_strength = higher_signal['signal']['signal_strength']
        breakout_detected = higher_signal['signal'].get('breakout_detected', False)

        if signal_type == 'BUY':
            if breakout_detected and confirmation_strength >= 0.7:
                return f"STRONG BUY - Breakout with high confirmation (Strength: {signal_strength:.2f})"
            elif confirmation_strength >= 0.7:
                return f"BUY - High timeframe confirmation (Strength: {signal_strength:.2f})"
            elif confirmation_strength >= 0.5:
                return f"WEAK BUY - Moderate confirmation (Strength: {signal_strength:.2f})"
            else:
                return f"HOLD - Insufficient confirmation for buy signal"

        elif signal_type == 'SELL':
            if breakout_detected and confirmation_strength >= 0.7:
                return f"STRONG SELL - Breakout with high confirmation (Strength: {signal_strength:.2f})"
            elif confirmation_strength >= 0.7:
                return f"SELL - High timeframe confirmation (Strength: {signal_strength:.2f})"
            elif confirmation_strength >= 0.5:
                return f"WEAK SELL - Moderate confirmation (Strength: {signal_strength:.2f})"
            else:
                return f"HOLD - Insufficient confirmation for sell signal"

        elif signal_type == 'TREND':
            return f"FOLLOW TREND - {higher_signal['signal']['condition']} (Strength: {signal_strength:.2f})"

        elif signal_type == 'BREAKOUT':
            return f"BREAKOUT ALERT - Monitor for direction (Strength: {signal_strength:.2f})"

        else:
            return "HOLD - Neutral signal"

    def validate_individual_signals_with_price_movement(self, timeframe_data: Dict[str, pd.DataFrame],
                                                       individual_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate each individual indicator signal with actual price movement
        """
        print(f"\n✅ VALIDATING INDIVIDUAL SIGNALS WITH PRICE MOVEMENT")
        print("=" * 60)

        # Find 1-minute data for validation
        one_min_data = None
        for tf, data in timeframe_data.items():
            if '1min' in tf:
                one_min_data = data
                break

        if one_min_data is None:
            print("⚠️ No 1-minute data available for validation")
            return individual_analysis

        # Get price columns
        price_columns = ['Close', 'CURRENT_PRICE', 'HLC3', 'OHLC4']
        price_col = None
        for col in price_columns:
            if col in one_min_data.columns:
                price_col = col
                break

        if price_col is None:
            print("⚠️ No price column found for validation")
            return individual_analysis

        print(f"📊 Using {price_col} for price movement validation")

        # Get price data
        price_data = one_min_data[price_col].dropna()
        if len(price_data) < 40:
            print("⚠️ Insufficient price data for validation")
            return individual_analysis

        # Define validation windows
        validation_windows = {
            'very_quick': (3, 10),    # 3-10 minutes
            'quick': (11, 20),        # 11-20 minutes
            'moderate': (21, 40),     # 21-40 minutes
            'slow': (41, 60)          # 41+ minutes
        }

        # Validate each indicator individually
        for indicator, analysis in individual_analysis.items():
            print(f"\n🔍 Validating {indicator} signals...")

            hierarchy_confirmation = analysis['hierarchy_confirmation']

            if not hierarchy_confirmation['higher_timeframe_signal']:
                continue

            higher_signal = hierarchy_confirmation['higher_timeframe_signal']['signal']
            signal_type = higher_signal['signal_type']

            if signal_type not in ['BUY', 'SELL']:
                continue

            # Get current price (latest available)
            current_price = price_data.iloc[-1]

            validation_results = {}

            # Check price movement in different windows
            for window_name, (start_min, end_min) in validation_windows.items():
                if len(price_data) > end_min:
                    # Get price at the start of validation window
                    start_price = price_data.iloc[-(start_min + 1)]

                    # Calculate price movement
                    price_change = (current_price - start_price) / start_price * 100

                    # Determine if movement matches signal direction
                    movement_correct = False
                    if signal_type == 'BUY' and price_change > 0.1:  # At least 0.1% up
                        movement_correct = True
                    elif signal_type == 'SELL' and price_change < -0.1:  # At least 0.1% down
                        movement_correct = True

                    validation_results[window_name] = {
                        'price_change_pct': price_change,
                        'movement_correct': movement_correct,
                        'start_price': start_price,
                        'current_price': current_price
                    }

                    status = "✅" if movement_correct else "❌"
                    print(f"   {window_name}: {price_change:.2f}% {status}")

            # Add validation results to analysis
            analysis['signal_validation'] = validation_results

            # Calculate overall validation score
            correct_predictions = sum(1 for result in validation_results.values()
                                    if result['movement_correct'])
            total_windows = len(validation_results)

            if total_windows > 0:
                validation_score = correct_predictions / total_windows
                analysis['validation_score'] = validation_score

                if validation_score >= 0.75:
                    analysis['validation_assessment'] = "EXCELLENT - Signal validated in most time windows"
                elif validation_score >= 0.5:
                    analysis['validation_assessment'] = "GOOD - Signal validated in majority of time windows"
                else:
                    analysis['validation_assessment'] = "POOR - Signal not validated by price movement"

        return individual_analysis

    def export_individual_analysis_to_excel(self, individual_analysis: Dict[str, Any],
                                          inputs: Dict[str, Any]) -> str:
        """
        Export individual indicator analysis to Excel format
        """
        print(f"\n📊 EXPORTING INDIVIDUAL ANALYSIS TO EXCEL")
        print("=" * 60)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"individual_indicator_analysis_{inputs['ticker']}_{inputs['exchange']}_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # Sheet 1: Summary
                summary_data = []
                for indicator, analysis in individual_analysis.items():
                    hierarchy = analysis['hierarchy_confirmation']
                    higher_signal = hierarchy.get('higher_timeframe_signal')

                    if higher_signal:
                        signal_info = higher_signal['signal']
                        summary_data.append({
                            'Indicator': indicator,
                            'Signal_Type': signal_info['signal_type'],
                            'Condition': signal_info['condition'],
                            'Timeframe': higher_signal['timeframe'],
                            'Signal_Strength': signal_info['signal_strength'],
                            'Breakout_Detected': signal_info.get('breakout_detected', False),
                            'Confirmation_Strength': hierarchy['confirmation_strength'],
                            'Trade_Recommendation': analysis['trade_recommendation'],
                            'Validation_Score': analysis.get('validation_score', 0.0),
                            'Overall_Assessment': analysis['overall_assessment']
                        })

                if summary_data:
                    summary_df = pd.DataFrame(summary_data)
                    # Sort by signal strength descending
                    summary_df = summary_df.sort_values('Signal_Strength', ascending=False)
                    summary_df.to_excel(writer, sheet_name='Summary', index=False)

                # Sheet 2: Detailed Signals by Timeframe
                detailed_data = []
                for indicator, analysis in individual_analysis.items():
                    for timeframe, signal in analysis['timeframe_signals'].items():
                        detailed_data.append({
                            'Indicator': indicator,
                            'Timeframe': timeframe,
                            'Value': signal['value'],
                            'Signal_Type': signal['signal_type'],
                            'Condition': signal['condition'],
                            'Signal_Strength': signal['signal_strength'],
                            'Breakout_Detected': signal.get('breakout_detected', False),
                            'Professional_Assessment': signal['professional_assessment']
                        })

                if detailed_data:
                    detailed_df = pd.DataFrame(detailed_data)
                    detailed_df.to_excel(writer, sheet_name='Detailed_Signals', index=False)

                # Sheet 3: Signal Validation
                validation_data = []
                for indicator, analysis in individual_analysis.items():
                    validation_results = analysis.get('signal_validation', {})
                    for window, result in validation_results.items():
                        validation_data.append({
                            'Indicator': indicator,
                            'Time_Window': window,
                            'Price_Change_Pct': result['price_change_pct'],
                            'Movement_Correct': result['movement_correct'],
                            'Start_Price': result['start_price'],
                            'Current_Price': result['current_price']
                        })

                if validation_data:
                    validation_df = pd.DataFrame(validation_data)
                    validation_df.to_excel(writer, sheet_name='Signal_Validation', index=False)

                # Sheet 4: Strong Buy Signals
                strong_buy_data = []
                for indicator, analysis in individual_analysis.items():
                    if 'STRONG BUY' in analysis['trade_recommendation']:
                        hierarchy = analysis['hierarchy_confirmation']
                        higher_signal = hierarchy.get('higher_timeframe_signal')
                        if higher_signal:
                            signal_info = higher_signal['signal']
                            strong_buy_data.append({
                                'Indicator': indicator,
                                'Timeframe': higher_signal['timeframe'],
                                'Signal_Strength': signal_info['signal_strength'],
                                'Condition': signal_info['condition'],
                                'Confirmation_Strength': hierarchy['confirmation_strength'],
                                'Validation_Score': analysis.get('validation_score', 0.0),
                                'Professional_Assessment': signal_info['professional_assessment']
                            })

                if strong_buy_data:
                    strong_buy_df = pd.DataFrame(strong_buy_data)
                    strong_buy_df = strong_buy_df.sort_values('Signal_Strength', ascending=False)
                    strong_buy_df.to_excel(writer, sheet_name='Strong_Buy_Signals', index=False)

                # Sheet 5: Strong Sell Signals
                strong_sell_data = []
                for indicator, analysis in individual_analysis.items():
                    if 'STRONG SELL' in analysis['trade_recommendation']:
                        hierarchy = analysis['hierarchy_confirmation']
                        higher_signal = hierarchy.get('higher_timeframe_signal')
                        if higher_signal:
                            signal_info = higher_signal['signal']
                            strong_sell_data.append({
                                'Indicator': indicator,
                                'Timeframe': higher_signal['timeframe'],
                                'Signal_Strength': signal_info['signal_strength'],
                                'Condition': signal_info['condition'],
                                'Confirmation_Strength': hierarchy['confirmation_strength'],
                                'Validation_Score': analysis.get('validation_score', 0.0),
                                'Professional_Assessment': signal_info['professional_assessment']
                            })

                if strong_sell_data:
                    strong_sell_df = pd.DataFrame(strong_sell_data)
                    strong_sell_df = strong_sell_df.sort_values('Signal_Strength', ascending=False)
                    strong_sell_df.to_excel(writer, sheet_name='Strong_Sell_Signals', index=False)

                # Sheet 6: Breakout Signals
                breakout_data = []
                for indicator, analysis in individual_analysis.items():
                    for timeframe, signal in analysis['timeframe_signals'].items():
                        if signal.get('breakout_detected', False):
                            breakout_data.append({
                                'Indicator': indicator,
                                'Timeframe': timeframe,
                                'Signal_Type': signal['signal_type'],
                                'Condition': signal['condition'],
                                'Signal_Strength': signal['signal_strength'],
                                'Professional_Assessment': signal['professional_assessment']
                            })

                if breakout_data:
                    breakout_df = pd.DataFrame(breakout_data)
                    breakout_df = breakout_df.sort_values('Signal_Strength', ascending=False)
                    breakout_df.to_excel(writer, sheet_name='Breakout_Signals', index=False)

            print(f"✅ Excel file saved: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error exporting to Excel: {str(e)}")
            return ""

    def run_complete_individual_analysis(self) -> Dict[str, Any]:
        """
        Run complete individual indicator analysis
        """
        print("\n🚀 INDIVIDUAL INDICATOR MULTI-TIMEFRAME ANALYZER")
        print("=" * 80)
        print("Analyzing each indicator separately across timeframes")
        print("Higher timeframe → Lower timeframe hierarchy")
        print("Overbought/Oversold + Breakout detection for each indicator")

        # Get user inputs
        inputs = self.get_user_inputs()

        # Find data files
        data_files = self.find_data_files(inputs['ticker'], inputs['exchange'], inputs['date'])

        if not data_files:
            print("❌ No matching data files found")
            return {}

        # Assign timeframes to files
        timeframe_files = self.assign_timeframes_to_files(data_files, inputs['timeframes'])

        if len(timeframe_files) < 2:
            print("❌ Need at least 2 timeframes for analysis")
            return {}

        # Load timeframe data
        timeframe_data = self.load_timeframe_data(timeframe_files)

        if len(timeframe_data) < 2:
            print("❌ Failed to load sufficient timeframe data")
            return {}

        # Filter indicators to only those we have thresholds for
        available_indicators = []
        for indicator in inputs['indicators']:
            if indicator in self.timeframe_thresholds:
                available_indicators.append(indicator)
            else:
                # Try to find partial matches
                for configured_indicator in self.timeframe_thresholds.keys():
                    if indicator in configured_indicator:
                        available_indicators.append(configured_indicator)
                        break

        if not available_indicators:
            print("❌ No configured indicators found matching your selection")
            print(f"Available indicators: {', '.join(self.timeframe_thresholds.keys())}")
            return {}

        print(f"\n📊 Analyzing {len(available_indicators)} individual indicators:")
        for indicator in available_indicators:
            print(f"   • {indicator}")

        # Perform individual indicator analysis
        individual_analysis = self.analyze_individual_indicators_across_timeframes(
            timeframe_data, available_indicators
        )

        # Validate signals if requested
        if inputs['validate_signals']:
            individual_analysis = self.validate_individual_signals_with_price_movement(
                timeframe_data, individual_analysis
            )

        # Export to Excel
        excel_filename = self.export_individual_analysis_to_excel(individual_analysis, inputs)

        # Print comprehensive summary
        self.print_individual_analysis_summary(individual_analysis)

        # Save JSON backup
        json_filename = f"individual_analysis_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_filename, 'w') as f:
            json.dump(individual_analysis, f, indent=2, default=str)

        print(f"\n💾 Results saved:")
        print(f"   📊 Excel: {excel_filename}")
        print(f"   📄 JSON: {json_filename}")

        return individual_analysis

    def print_individual_analysis_summary(self, individual_analysis: Dict[str, Any]):
        """Print comprehensive summary of individual indicator analysis"""
        print(f"\n📊 INDIVIDUAL INDICATOR ANALYSIS SUMMARY")
        print("=" * 80)

        # Count signals by type
        strong_buy_count = 0
        strong_sell_count = 0
        buy_count = 0
        sell_count = 0
        breakout_count = 0
        validated_signals = 0

        for indicator, analysis in individual_analysis.items():
            trade_rec = analysis['trade_recommendation']

            if 'STRONG BUY' in trade_rec:
                strong_buy_count += 1
            elif 'STRONG SELL' in trade_rec:
                strong_sell_count += 1
            elif 'BUY' in trade_rec:
                buy_count += 1
            elif 'SELL' in trade_rec:
                sell_count += 1

            # Count breakouts
            for timeframe, signal in analysis['timeframe_signals'].items():
                if signal.get('breakout_detected', False):
                    breakout_count += 1
                    break  # Count each indicator only once

            # Count validated signals
            if analysis.get('validation_score', 0) >= 0.5:
                validated_signals += 1

        print(f"🎯 SIGNAL SUMMARY:")
        print(f"   🟢 Strong Buy Signals: {strong_buy_count}")
        print(f"   🔴 Strong Sell Signals: {strong_sell_count}")
        print(f"   🟡 Buy Signals: {buy_count}")
        print(f"   🟡 Sell Signals: {sell_count}")
        print(f"   💥 Breakout Signals: {breakout_count}")
        print(f"   ✅ Validated Signals: {validated_signals}")

        # Show top signals
        print(f"\n🏆 TOP INDIVIDUAL INDICATOR SIGNALS:")

        # Sort by signal strength
        sorted_indicators = []
        for indicator, analysis in individual_analysis.items():
            hierarchy = analysis['hierarchy_confirmation']
            if hierarchy.get('higher_timeframe_signal'):
                signal_strength = hierarchy['higher_timeframe_signal']['signal']['signal_strength']
                sorted_indicators.append((indicator, analysis, signal_strength))

        sorted_indicators.sort(key=lambda x: x[2], reverse=True)

        for i, (indicator, analysis, strength) in enumerate(sorted_indicators[:10]):
            hierarchy = analysis['hierarchy_confirmation']
            higher_signal = hierarchy['higher_timeframe_signal']
            signal_info = higher_signal['signal']

            print(f"   {i+1:2d}. {indicator}")
            print(f"       Signal: {signal_info['signal_type']} - {signal_info['condition']}")
            print(f"       Timeframe: {higher_signal['timeframe']}")
            print(f"       Strength: {strength:.3f}")
            print(f"       Recommendation: {analysis['trade_recommendation']}")

            validation_score = analysis.get('validation_score', 0)
            if validation_score > 0:
                print(f"       Validation: {validation_score:.1%}")
            print()


def main():
    """
    Main execution function
    """
    analyzer = IndividualIndicatorMultiTimeframeAnalyzer()
    results = analyzer.run_complete_individual_analysis()

    if results:
        print("\n✅ Individual indicator analysis completed successfully!")
        print("📊 Each indicator analyzed separately across all timeframes")
        print("🎯 Professional overbought/oversold and breakout detection")
        print("✅ Signal validation with actual price movement")
    else:
        print("\n❌ Analysis failed.")


if __name__ == "__main__":
    main()
