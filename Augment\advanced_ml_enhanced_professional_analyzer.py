"""
Advanced ML-Enhanced Professional Signal Analyzer
- Real time format (not Period_1, Period_2)
- Proper stop loss and targets for PGO
- ML-based threshold optimization for near 1.0 accuracy
- Separate thresholds for reversals vs breakouts
- Advanced analytics for overbought/oversold detection
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Tuple, Optional
import glob
import os
from scipy import stats
from scipy.optimize import minimize, differential_evolution
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, RandomForestRegressor
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, classification_report, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

class AdvancedMLEnhancedProfessionalAnalyzer:
    def __init__(self):
        print("🚀 Advanced ML-Enhanced Professional Signal Analyzer initialized")
        print("⏰ Real time format implementation")
        print("🎯 ML-based threshold optimization for near 1.0 accuracy")
        print("🔄 Separate reversal vs breakout thresholds")
        print("📊 Advanced overbought/oversold detection")
        print("🤖 Advanced ML Learning & Optimization System")
        print("📈 True Signal Identification & Validation")
        print("🔍 Mathematical Optimization Functions")

        # Initialize ML models and scalers
        self.ml_models = {
            'random_forest': RandomForestRegressor(n_estimators=200, random_state=42),
            'gradient_boost': GradientBoostingClassifier(n_estimators=200, random_state=42),
            'neural_network': MLPRegressor(hidden_layer_sizes=(100, 50), random_state=42),
            'svm': SVR(kernel='rbf')
        }
        self.scaler = StandardScaler()
        self.learning_results = {}
        self.optimized_thresholds = {}
        self.true_signals_database = []
        
        # Enhanced professional thresholds with ML optimization
        self.ml_optimized_thresholds = {
            'PGO_14': {
                'reversal': {
                    'oversold_entry': -4.5, 'oversold_exit': -2.0,
                    'overbought_entry': 4.5, 'overbought_exit': 2.0,
                    'stop_loss_pct': 2.5, 'target_pct': 5.0
                },
                'breakout': {
                    'oversold_breakout': -6.0, 'oversold_continuation': -4.0,
                    'overbought_breakout': 6.0, 'overbought_continuation': 4.0,
                    'stop_loss_pct': 1.5, 'target_pct': 3.0
                }
            },
            'CCI_14': {
                'reversal': {
                    'oversold_entry': -150, 'oversold_exit': -50,
                    'overbought_entry': 150, 'overbought_exit': 50,
                    'stop_loss_pct': 2.0, 'target_pct': 4.0
                },
                'breakout': {
                    'oversold_breakout': -200, 'oversold_continuation': -100,
                    'overbought_breakout': 200, 'overbought_continuation': 100,
                    'stop_loss_pct': 1.5, 'target_pct': 3.5
                }
            },
            'SMI_5_20_5_SMIo_5_20_5_100.0': {
                'reversal': {
                    'oversold_entry': -40, 'oversold_exit': -20,
                    'overbought_entry': 40, 'overbought_exit': 20,
                    'stop_loss_pct': 2.0, 'target_pct': 4.5
                },
                'breakout': {
                    'oversold_breakout': -50, 'oversold_continuation': -30,
                    'overbought_breakout': 50, 'overbought_continuation': 30,
                    'stop_loss_pct': 1.5, 'target_pct': 3.0
                }
            },
            'BIAS_26': {
                'reversal': {
                    'oversold_entry': -8, 'oversold_exit': -3,
                    'overbought_entry': 8, 'overbought_exit': 3,
                    'stop_loss_pct': 2.5, 'target_pct': 5.5
                },
                'breakout': {
                    'oversold_breakout': -12, 'oversold_continuation': -6,
                    'overbought_breakout': 12, 'overbought_continuation': 6,
                    'stop_loss_pct': 1.5, 'target_pct': 3.5
                }
            },
            'CG_10': {
                'reversal': {
                    'oversold_entry': -15, 'oversold_exit': -5,
                    'overbought_entry': 15, 'overbought_exit': 5,
                    'stop_loss_pct': 2.0, 'target_pct': 4.0
                },
                'breakout': {
                    'oversold_breakout': -25, 'oversold_continuation': -10,
                    'overbought_breakout': 25, 'overbought_continuation': 10,
                    'stop_loss_pct': 1.5, 'target_pct': 3.0
                }
            }
        }
        
        # ML models for advanced optimization
        self.ml_models = {}
        self.scalers = {}
        
    def convert_periods_to_real_time(self, period_index: int, base_time: str, interval_minutes: int) -> str:
        """Convert Period_X to actual time format"""
        try:
            # Parse base time (assuming format like "10:00")
            if ':' in base_time:
                hour, minute = map(int, base_time.split(':'))
                base_datetime = datetime.now().replace(hour=hour, minute=minute, second=0, microsecond=0)
            else:
                # If it's already a time string, try to parse it
                base_datetime = datetime.strptime(base_time, "%H:%M")
            
            # Add interval minutes for each period
            actual_time = base_datetime + timedelta(minutes=period_index * interval_minutes)
            return actual_time.strftime("%H:%M")
        except:
            # Fallback to period format if conversion fails
            return f"Period_{period_index}"
    
    def extract_time_from_column(self, time_column: str) -> str:
        """Extract actual time from time column names"""
        try:
            # Handle various time formats in column names
            if 'Period_' in time_column:
                # Extract period number and convert
                period_num = int(time_column.split('_')[1])
                return self.convert_periods_to_real_time(period_num, "10:00", 1)  # Default 1-min intervals
            elif ':' in time_column:
                # Already in time format
                return time_column
            else:
                # Try to extract time pattern
                import re
                time_match = re.search(r'(\d{1,2}):(\d{2})', time_column)
                if time_match:
                    return f"{time_match.group(1)}:{time_match.group(2)}"
                else:
                    return time_column
        except:
            return time_column
    
    def calculate_proper_stop_loss_targets(self, indicator: str, signal_type: str, 
                                         strategy_type: str, current_price: float) -> Dict[str, float]:
        """Calculate proper stop loss and targets based on indicator and strategy"""
        
        if indicator not in self.ml_optimized_thresholds:
            return {'stop_loss': 0, 'target': 0}
        
        thresholds = self.ml_optimized_thresholds[indicator][strategy_type]
        
        if signal_type.upper() == 'BUY':
            stop_loss_pct = thresholds['stop_loss_pct']
            target_pct = thresholds['target_pct']
            
            stop_loss = current_price * (1 - stop_loss_pct / 100)
            target = current_price * (1 + target_pct / 100)
        else:  # SELL
            stop_loss_pct = thresholds['stop_loss_pct']
            target_pct = thresholds['target_pct']
            
            stop_loss = current_price * (1 + stop_loss_pct / 100)
            target = current_price * (1 - target_pct / 100)
        
        return {
            'stop_loss': round(stop_loss, 2),
            'target': round(target, 2),
            'stop_loss_pct': stop_loss_pct,
            'target_pct': target_pct
        }
    
    def advanced_ml_threshold_optimization(self, df: pd.DataFrame, indicator: str) -> Dict[str, Any]:
        """Use ML to optimize thresholds for near 1.0 accuracy"""
        
        print(f"🤖 ML optimizing thresholds for {indicator}...")
        
        if indicator not in df.columns:
            return {'accuracy': 0, 'optimized_thresholds': {}}
        
        # Prepare features for ML
        indicator_values = df[indicator].dropna()
        
        if len(indicator_values) < 50:  # Need sufficient data
            return {'accuracy': 0, 'optimized_thresholds': {}}
        
        # Create price movement features
        if 'Close' in df.columns:
            close_prices = df['Close'].dropna()
            
            # Align indicator and price data
            min_len = min(len(indicator_values), len(close_prices))
            indicator_values = indicator_values.iloc[:min_len]
            close_prices = close_prices.iloc[:min_len]
            
            # Calculate future price movements (targets)
            future_returns_1 = close_prices.pct_change(1).shift(-1)  # 1-period ahead
            future_returns_3 = close_prices.pct_change(3).shift(-3)  # 3-period ahead
            future_returns_5 = close_prices.pct_change(5).shift(-5)  # 5-period ahead
            
            # Create features
            features = pd.DataFrame({
                'indicator_value': indicator_values,
                'indicator_sma_5': indicator_values.rolling(5).mean(),
                'indicator_sma_10': indicator_values.rolling(10).mean(),
                'indicator_std_5': indicator_values.rolling(5).std(),
                'indicator_roc_1': indicator_values.pct_change(1),
                'indicator_roc_3': indicator_values.pct_change(3),
                'price_sma_5': close_prices.rolling(5).mean(),
                'price_sma_10': close_prices.rolling(10).mean(),
                'price_volatility': close_prices.rolling(10).std(),
            })
            
            # Create targets for different strategies
            reversal_target = np.where(
                (future_returns_3 > 0.02) | (future_returns_3 < -0.02), 1, 0
            )  # Strong reversal movements
            
            breakout_target = np.where(
                (future_returns_1 > 0.01) & (future_returns_5 > 0.03), 1, 0
            )  # Sustained breakout movements
            
            # Remove NaN values
            features = features.dropna()
            min_len = min(len(features), len(reversal_target), len(breakout_target))
            
            if min_len < 30:
                return {'accuracy': 0, 'optimized_thresholds': {}}
            
            features = features.iloc[:min_len]
            reversal_target = reversal_target[:min_len]
            breakout_target = breakout_target[:min_len]
            
            # Train ML models for both strategies
            results = {}
            
            for strategy, target in [('reversal', reversal_target), ('breakout', breakout_target)]:
                try:
                    # Split data
                    X_train, X_test, y_train, y_test = train_test_split(
                        features, target, test_size=0.3, random_state=42
                    )
                    
                    # Scale features
                    scaler = StandardScaler()
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_test_scaled = scaler.transform(X_test)
                    
                    # Train Random Forest with hyperparameter tuning
                    rf = RandomForestClassifier(random_state=42)
                    param_grid = {
                        'n_estimators': [50, 100, 200],
                        'max_depth': [5, 10, 15],
                        'min_samples_split': [2, 5, 10]
                    }
                    
                    grid_search = GridSearchCV(rf, param_grid, cv=3, scoring='accuracy')
                    grid_search.fit(X_train_scaled, y_train)
                    
                    # Best model predictions
                    best_model = grid_search.best_estimator_
                    y_pred = best_model.predict(X_test_scaled)
                    
                    accuracy = accuracy_score(y_test, y_pred)
                    precision = precision_score(y_test, y_pred, average='weighted')
                    recall = recall_score(y_test, y_pred, average='weighted')
                    
                    # Feature importance for threshold optimization
                    feature_importance = best_model.feature_importances_
                    
                    # Optimize thresholds based on feature importance and predictions
                    indicator_importance = feature_importance[0]  # First feature is indicator_value
                    
                    # Calculate optimized thresholds
                    indicator_percentiles = np.percentile(indicator_values, [10, 25, 75, 90])
                    
                    if strategy == 'reversal':
                        optimized_oversold = indicator_percentiles[0] * (1 + indicator_importance)
                        optimized_overbought = indicator_percentiles[3] * (1 + indicator_importance)
                        optimized_oversold_exit = indicator_percentiles[1]
                        optimized_overbought_exit = indicator_percentiles[2]
                    else:  # breakout
                        optimized_oversold = indicator_percentiles[0] * (1 - indicator_importance * 0.5)
                        optimized_overbought = indicator_percentiles[3] * (1 + indicator_importance * 0.5)
                        optimized_oversold_continuation = indicator_percentiles[1] * (1 + indicator_importance * 0.3)
                        optimized_overbought_continuation = indicator_percentiles[2] * (1 + indicator_importance * 0.3)
                    
                    results[strategy] = {
                        'accuracy': accuracy,
                        'precision': precision,
                        'recall': recall,
                        'feature_importance': indicator_importance,
                        'optimized_thresholds': {
                            'oversold': optimized_oversold,
                            'overbought': optimized_overbought,
                            'oversold_exit': optimized_oversold_exit if strategy == 'reversal' else optimized_oversold_continuation,
                            'overbought_exit': optimized_overbought_exit if strategy == 'reversal' else optimized_overbought_continuation,
                        }
                    }
                    
                    # Store model for future use
                    self.ml_models[f"{indicator}_{strategy}"] = best_model
                    self.scalers[f"{indicator}_{strategy}"] = scaler
                    
                    print(f"   ✅ {strategy}: {accuracy:.3f} accuracy, {precision:.3f} precision")
                    
                except Exception as e:
                    print(f"   ❌ {strategy}: ML optimization failed - {str(e)}")
                    results[strategy] = {'accuracy': 0, 'optimized_thresholds': {}}
            
            return results
        
        return {'accuracy': 0, 'optimized_thresholds': {}}
    
    def detect_advanced_signals_with_real_time(self, timeframe_data: Dict[str, pd.DataFrame], 
                                             indicators: List[str]) -> Dict[str, Any]:
        """Detect signals with real time format and proper stop loss/targets"""
        
        print(f"\n🎯 ADVANCED SIGNAL DETECTION WITH REAL TIME")
        print("=" * 80)
        
        all_signals = []
        ml_optimization_results = {}
        
        for indicator in indicators:
            print(f"\n🔍 Analyzing {indicator} with ML optimization...")
            
            # ML optimize thresholds for this indicator
            for timeframe, df in timeframe_data.items():
                if indicator in df.columns:
                    ml_results = self.advanced_ml_threshold_optimization(df, indicator)
                    ml_optimization_results[f"{indicator}_{timeframe}"] = ml_results
                    break
            
            # Detect signals across all timeframes
            for timeframe, df in timeframe_data.items():
                if indicator not in df.columns:
                    continue
                
                print(f"   📊 {timeframe}: {df.shape}")
                
                # Get interval minutes from timeframe
                interval_minutes = int(timeframe.replace('min', ''))
                
                # Get close prices for stop loss/target calculation
                close_prices = df.get('Close', pd.Series([100] * len(df)))  # Default price if not available
                
                indicator_values = df[indicator].dropna()
                
                for idx, (time_col, value) in enumerate(indicator_values.items()):
                    if pd.isna(value):
                        continue
                    
                    # Convert time to real format
                    real_time = self.extract_time_from_column(str(time_col))
                    current_price = close_prices.iloc[idx] if idx < len(close_prices) else 100
                    
                    # Check for reversal signals
                    reversal_signal = self._check_reversal_signal(indicator, value, timeframe)
                    if reversal_signal:
                        # Calculate proper stop loss and targets
                        sl_target = self.calculate_proper_stop_loss_targets(
                            indicator, reversal_signal['signal_type'], 'reversal', current_price
                        )
                        
                        signal = {
                            'indicator': indicator,
                            'timeframe': timeframe,
                            'signal_type': reversal_signal['signal_type'],
                            'strategy': 'REVERSAL',
                            'generation_time': real_time,
                            'confirmation_time': self._calculate_confirmation_time(real_time, interval_minutes, 2),
                            'entry_time': self._calculate_confirmation_time(real_time, interval_minutes, 1),
                            'indicator_value': round(value, 3),
                            'current_price': current_price,
                            'stop_loss': sl_target['stop_loss'],
                            'target': sl_target['target'],
                            'stop_loss_pct': sl_target['stop_loss_pct'],
                            'target_pct': sl_target['target_pct'],
                            'reason': reversal_signal['reason'],
                            'strength': reversal_signal['strength']
                        }
                        all_signals.append(signal)
                    
                    # Check for breakout signals
                    breakout_signal = self._check_breakout_signal(indicator, value, timeframe)
                    if breakout_signal:
                        # Calculate proper stop loss and targets
                        sl_target = self.calculate_proper_stop_loss_targets(
                            indicator, breakout_signal['signal_type'], 'breakout', current_price
                        )
                        
                        signal = {
                            'indicator': indicator,
                            'timeframe': timeframe,
                            'signal_type': breakout_signal['signal_type'],
                            'strategy': 'BREAKOUT',
                            'generation_time': real_time,
                            'confirmation_time': self._calculate_confirmation_time(real_time, interval_minutes, 1),
                            'entry_time': real_time,  # Immediate entry for breakouts
                            'indicator_value': round(value, 3),
                            'current_price': current_price,
                            'stop_loss': sl_target['stop_loss'],
                            'target': sl_target['target'],
                            'stop_loss_pct': sl_target['stop_loss_pct'],
                            'target_pct': sl_target['target_pct'],
                            'reason': breakout_signal['reason'],
                            'strength': breakout_signal['strength']
                        }
                        all_signals.append(signal)
        
        return {
            'signals': all_signals,
            'ml_optimization_results': ml_optimization_results,
            'total_signals': len(all_signals)
        }
    
    def _calculate_confirmation_time(self, base_time: str, interval_minutes: int, periods_ahead: int) -> str:
        """Calculate confirmation time by adding periods"""
        try:
            if ':' in base_time:
                hour, minute = map(int, base_time.split(':'))
                base_datetime = datetime.now().replace(hour=hour, minute=minute, second=0, microsecond=0)
                confirmation_datetime = base_datetime + timedelta(minutes=interval_minutes * periods_ahead)
                return confirmation_datetime.strftime("%H:%M")
            else:
                return base_time
        except:
            return base_time
    
    def _check_reversal_signal(self, indicator: str, value: float, timeframe: str) -> Optional[Dict[str, Any]]:
        """Check for reversal signals using ML-optimized thresholds"""
        
        if indicator not in self.ml_optimized_thresholds:
            return None
        
        thresholds = self.ml_optimized_thresholds[indicator]['reversal']
        
        # Oversold reversal (BUY signal)
        if value <= thresholds['oversold_entry']:
            return {
                'signal_type': 'BUY',
                'reason': f'REVERSAL_OVERSOLD (≤{thresholds["oversold_entry"]})',
                'strength': min(abs(value / thresholds['oversold_entry']), 2.0)
            }
        
        # Overbought reversal (SELL signal)
        if value >= thresholds['overbought_entry']:
            return {
                'signal_type': 'SELL',
                'reason': f'REVERSAL_OVERBOUGHT (≥{thresholds["overbought_entry"]})',
                'strength': min(abs(value / thresholds['overbought_entry']), 2.0)
            }
        
        return None

    def identify_true_signals_with_validation(self, data_1min: pd.DataFrame,
                                            higher_timeframe_data: Dict[str, pd.DataFrame],
                                            indicators: List[str]) -> Dict[str, Any]:
        """
        🎯 ADVANCED TRUE SIGNAL IDENTIFICATION & VALIDATION
        - Identify 1min signals that were actually profitable within 15 minutes
        - Analyze higher timeframe values at signal time
        - Learn optimal thresholds from actual market behavior
        """
        print("\n🎯 ADVANCED TRUE SIGNAL IDENTIFICATION & VALIDATION")
        print("================================================================================")

        true_signals = []
        false_signals = []

        for indicator in indicators:
            if indicator not in data_1min.columns:
                continue

            print(f"\n🔍 Analyzing {indicator} for true signal identification...")

            # Get 1min signals using current thresholds
            signals_1min = self._detect_1min_signals(data_1min, indicator)

            for signal in signals_1min:
                # Validate if signal was profitable within 15 minutes
                validation_result = self._validate_signal_profitability(
                    data_1min, signal, validation_minutes=15
                )

                if validation_result['is_profitable']:
                    # Get higher timeframe values at signal time
                    htf_values = self._get_higher_timeframe_values_at_signal(
                        higher_timeframe_data, signal, indicator
                    )

                    true_signal_data = {
                        'indicator': indicator,
                        'signal_time': signal['time'],
                        'signal_type': signal['type'],
                        'signal_value': signal['value'],
                        'profit_achieved': validation_result['profit_pct'],
                        'time_to_profit': validation_result['time_to_profit'],
                        'higher_timeframe_values': htf_values,
                        'market_context': self._analyze_market_context(data_1min, signal)
                    }
                    true_signals.append(true_signal_data)
                else:
                    false_signals.append({
                        'indicator': indicator,
                        'signal_time': signal['time'],
                        'signal_type': signal['type'],
                        'signal_value': signal['value'],
                        'loss_incurred': validation_result.get('loss_pct', 0)
                    })

        # Store results for ML learning
        self.true_signals_database = true_signals

        return {
            'true_signals': true_signals,
            'false_signals': false_signals,
            'true_signal_rate': len(true_signals) / (len(true_signals) + len(false_signals)) if (len(true_signals) + len(false_signals)) > 0 else 0,
            'total_signals_analyzed': len(true_signals) + len(false_signals)
        }

    def advanced_ml_threshold_optimization(self, true_signals: List[Dict],
                                         indicators: List[str],
                                         optimization_rounds: int = 5) -> Dict[str, Any]:
        """
        🤖 ADVANCED ML THRESHOLD OPTIMIZATION
        - Multiple ML algorithms for maximum learning
        - Mathematical optimization functions
        - Cross-validation and ensemble methods
        """
        print("\n🤖 ADVANCED ML THRESHOLD OPTIMIZATION")
        print("================================================================================")

        optimization_results = {}

        for round_num in range(optimization_rounds):
            print(f"\n🔄 Optimization Round {round_num + 1}/{optimization_rounds}")

            for indicator in indicators:
                print(f"\n📊 Optimizing {indicator} thresholds...")

                # Prepare training data from true signals
                X, y = self._prepare_ml_training_data(true_signals, indicator)

                if len(X) < 10:  # Need minimum data for ML
                    print(f"⚠️ Insufficient data for {indicator} ML optimization")
                    continue

                # Multiple ML approaches
                ml_results = {}

                # 1. Random Forest Optimization
                rf_result = self._optimize_with_random_forest(X, y, indicator)
                ml_results['random_forest'] = rf_result

                # 2. Gradient Boosting Optimization
                gb_result = self._optimize_with_gradient_boosting(X, y, indicator)
                ml_results['gradient_boosting'] = gb_result

                # 3. Neural Network Optimization
                nn_result = self._optimize_with_neural_network(X, y, indicator)
                ml_results['neural_network'] = nn_result

                # 4. Mathematical Optimization
                math_result = self._mathematical_optimization(true_signals, indicator)
                ml_results['mathematical'] = math_result

                # 5. Ensemble Method
                ensemble_result = self._ensemble_optimization(ml_results, indicator)
                ml_results['ensemble'] = ensemble_result

                optimization_results[indicator] = ml_results

                # Update optimized thresholds with best result
                best_method = max(ml_results.keys(), key=lambda k: ml_results[k]['accuracy'])
                self.optimized_thresholds[indicator] = ml_results[best_method]['thresholds']

                print(f"✅ Best method for {indicator}: {best_method} (Accuracy: {ml_results[best_method]['accuracy']:.3f})")

        return optimization_results

    def _detect_1min_signals(self, data: pd.DataFrame, indicator: str) -> List[Dict]:
        """Detect 1min signals using current thresholds"""
        signals = []

        if indicator not in data.columns:
            return signals

        # Use current professional thresholds for detection
        current_thresholds = self._get_current_thresholds(indicator)

        for i in range(1, len(data)):
            value = data[indicator].iloc[i]
            prev_value = data[indicator].iloc[i-1]

            # BUY signal detection
            if (prev_value <= current_thresholds['detection_oversold'] and
                value >= current_thresholds['confirmation_oversold']):
                signals.append({
                    'time': data.index[i],
                    'type': 'BUY',
                    'value': value,
                    'detection_value': prev_value,
                    'price': data['close'].iloc[i] if 'close' in data.columns else None
                })

            # SELL signal detection
            if (prev_value >= current_thresholds['detection_overbought'] and
                value <= current_thresholds['confirmation_overbought']):
                signals.append({
                    'time': data.index[i],
                    'type': 'SELL',
                    'value': value,
                    'detection_value': prev_value,
                    'price': data['close'].iloc[i] if 'close' in data.columns else None
                })

        return signals

    def _validate_signal_profitability(self, data: pd.DataFrame, signal: Dict,
                                     validation_minutes: int = 15) -> Dict:
        """Validate if signal was profitable within specified time"""
        signal_time = signal['time']
        signal_type = signal['type']
        signal_price = signal.get('price')

        if signal_price is None:
            return {'is_profitable': False, 'reason': 'No price data'}

        # Find data points within validation window
        end_time = signal_time + pd.Timedelta(minutes=validation_minutes)
        future_data = data[data.index > signal_time]
        future_data = future_data[future_data.index <= end_time]

        if len(future_data) == 0:
            return {'is_profitable': False, 'reason': 'No future data'}

        if signal_type == 'BUY':
            # Check for profit in BUY signal
            max_price = future_data['close'].max()
            profit_pct = ((max_price - signal_price) / signal_price) * 100

            if profit_pct >= 1.0:  # At least 1% profit
                profit_time_idx = future_data['close'].idxmax()
                time_to_profit = (profit_time_idx - signal_time).total_seconds() / 60
                return {
                    'is_profitable': True,
                    'profit_pct': profit_pct,
                    'time_to_profit': time_to_profit,
                    'max_price': max_price
                }

        elif signal_type == 'SELL':
            # Check for profit in SELL signal
            min_price = future_data['close'].min()
            profit_pct = ((signal_price - min_price) / signal_price) * 100

            if profit_pct >= 1.0:  # At least 1% profit
                profit_time_idx = future_data['close'].idxmin()
                time_to_profit = (profit_time_idx - signal_time).total_seconds() / 60
                return {
                    'is_profitable': True,
                    'profit_pct': profit_pct,
                    'time_to_profit': time_to_profit,
                    'min_price': min_price
                }

        return {'is_profitable': False, 'reason': 'Insufficient profit'}

    def _get_higher_timeframe_values_at_signal(self, htf_data: Dict[str, pd.DataFrame],
                                             signal: Dict, indicator: str) -> Dict:
        """Get higher timeframe indicator values at signal time"""
        signal_time = signal['time']
        htf_values = {}

        for timeframe, data in htf_data.items():
            if indicator not in data.columns:
                continue

            # Find closest time point
            closest_idx = data.index.get_indexer([signal_time], method='nearest')[0]
            if closest_idx >= 0 and closest_idx < len(data):
                htf_values[timeframe] = {
                    'value': data[indicator].iloc[closest_idx],
                    'time': data.index[closest_idx]
                }

        return htf_values

    def _analyze_market_context(self, data: pd.DataFrame, signal: Dict) -> Dict:
        """Analyze market context around signal time"""
        signal_time = signal['time']
        signal_idx = data.index.get_indexer([signal_time], method='nearest')[0]

        # Get context window (10 minutes before and after)
        start_idx = max(0, signal_idx - 10)
        end_idx = min(len(data), signal_idx + 10)
        context_data = data.iloc[start_idx:end_idx]

        if 'close' not in context_data.columns:
            return {}

        return {
            'volatility': context_data['close'].std(),
            'trend': 'up' if context_data['close'].iloc[-1] > context_data['close'].iloc[0] else 'down',
            'price_range': context_data['close'].max() - context_data['close'].min(),
            'volume_avg': context_data['volume'].mean() if 'volume' in context_data.columns else None
        }

    def _get_current_thresholds(self, indicator: str) -> Dict:
        """Get current professional thresholds for indicator"""
        # Default professional thresholds (will be updated by ML)
        default_thresholds = {
            'PGO_14': {
                'detection_oversold': -3.2, 'confirmation_oversold': -2.4,
                'detection_overbought': 3.2, 'confirmation_overbought': 2.4
            },
            'CCI_14': {
                'detection_oversold': -110, 'confirmation_oversold': -70,
                'detection_overbought': 110, 'confirmation_overbought': 70
            },
            'SMI_5_20_5_SMIo_5_20_5_100.0': {
                'detection_oversold': -35, 'confirmation_oversold': -25,
                'detection_overbought': 35, 'confirmation_overbought': 25
            },
            'BIAS_26': {
                'detection_oversold': -5.5, 'confirmation_oversold': -3.5,
                'detection_overbought': 5.5, 'confirmation_overbought': 3.5
            },
            'CG_10': {
                'detection_oversold': -10.0, 'confirmation_oversold': -7.0,
                'detection_overbought': 10.0, 'confirmation_overbought': 7.0
            }
        }

        return default_thresholds.get(indicator, {
            'detection_oversold': -2.0, 'confirmation_oversold': -1.0,
            'detection_overbought': 2.0, 'confirmation_overbought': 1.0
        })

    def _prepare_ml_training_data(self, true_signals: List[Dict], indicator: str) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare training data for ML optimization"""
        indicator_signals = [s for s in true_signals if s['indicator'] == indicator]

        if len(indicator_signals) == 0:
            return np.array([]), np.array([])

        features = []
        targets = []

        for signal in indicator_signals:
            # Features: signal value, higher timeframe values, market context
            feature_vector = [signal['signal_value']]

            # Add higher timeframe values
            htf_values = signal.get('higher_timeframe_values', {})
            for tf in ['3min', '5min', '15min', '30min', '60min']:
                if tf in htf_values:
                    feature_vector.append(htf_values[tf]['value'])
                else:
                    feature_vector.append(0)  # Default value

            # Add market context
            context = signal.get('market_context', {})
            feature_vector.extend([
                context.get('volatility', 0),
                context.get('price_range', 0),
                1 if context.get('trend') == 'up' else 0
            ])

            features.append(feature_vector)
            targets.append(signal['profit_achieved'])

        return np.array(features), np.array(targets)

    def _optimize_with_random_forest(self, X: np.ndarray, y: np.ndarray, indicator: str) -> Dict:
        """Optimize thresholds using Random Forest"""
        if len(X) == 0:
            return {'accuracy': 0, 'thresholds': {}}

        # Scale features
        X_scaled = self.scaler.fit_transform(X)

        # Train Random Forest
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X_scaled, y)

        # Cross-validation score
        cv_scores = cross_val_score(rf, X_scaled, y, cv=min(5, len(X)))
        accuracy = cv_scores.mean()

        # Feature importance analysis
        feature_importance = rf.feature_importances_

        # Optimize thresholds based on feature importance
        optimized_thresholds = self._calculate_optimized_thresholds(
            X, y, feature_importance, indicator, method='random_forest'
        )

        return {
            'accuracy': accuracy,
            'thresholds': optimized_thresholds,
            'feature_importance': feature_importance,
            'cv_std': cv_scores.std()
        }

    def _optimize_with_gradient_boosting(self, X: np.ndarray, y: np.ndarray, indicator: str) -> Dict:
        """Optimize thresholds using Gradient Boosting"""
        if len(X) == 0:
            return {'accuracy': 0, 'thresholds': {}}

        # Convert to classification problem (profitable vs not)
        y_class = (y >= 1.0).astype(int)  # 1% profit threshold

        # Scale features
        X_scaled = self.scaler.fit_transform(X)

        # Train Gradient Boosting
        gb = GradientBoostingClassifier(n_estimators=100, random_state=42)
        gb.fit(X_scaled, y_class)

        # Cross-validation score
        cv_scores = cross_val_score(gb, X_scaled, y_class, cv=min(5, len(X)))
        accuracy = cv_scores.mean()

        # Feature importance analysis
        feature_importance = gb.feature_importances_

        # Optimize thresholds
        optimized_thresholds = self._calculate_optimized_thresholds(
            X, y, feature_importance, indicator, method='gradient_boosting'
        )

        return {
            'accuracy': accuracy,
            'thresholds': optimized_thresholds,
            'feature_importance': feature_importance,
            'cv_std': cv_scores.std()
        }

    def _optimize_with_neural_network(self, X: np.ndarray, y: np.ndarray, indicator: str) -> Dict:
        """Optimize thresholds using Neural Network"""
        if len(X) == 0:
            return {'accuracy': 0, 'thresholds': {}}

        # Scale features
        X_scaled = self.scaler.fit_transform(X)

        # Train Neural Network
        nn = MLPRegressor(hidden_layer_sizes=(50, 25), random_state=42, max_iter=1000)
        nn.fit(X_scaled, y)

        # Calculate R² score as accuracy measure
        y_pred = nn.predict(X_scaled)
        accuracy = r2_score(y, y_pred)

        # Optimize thresholds using neural network insights
        optimized_thresholds = self._calculate_optimized_thresholds(
            X, y, None, indicator, method='neural_network'
        )

        return {
            'accuracy': max(0, accuracy),  # Ensure non-negative
            'thresholds': optimized_thresholds,
            'mse': mean_squared_error(y, y_pred)
        }

    def _mathematical_optimization(self, true_signals: List[Dict], indicator: str) -> Dict:
        """Mathematical optimization using statistical analysis"""
        indicator_signals = [s for s in true_signals if s['indicator'] == indicator]

        if len(indicator_signals) == 0:
            return {'accuracy': 0, 'thresholds': {}}

        # Statistical analysis of successful signals
        signal_values = [s['signal_value'] for s in indicator_signals]
        profits = [s['profit_achieved'] for s in indicator_signals]

        # Calculate optimal thresholds using statistical methods
        mean_signal = np.mean(signal_values)
        std_signal = np.std(signal_values)

        # Correlation analysis
        correlation = np.corrcoef(signal_values, profits)[0, 1] if len(signal_values) > 1 else 0

        # Optimize based on profit distribution
        high_profit_signals = [s for s in indicator_signals if s['profit_achieved'] >= 2.0]

        if high_profit_signals:
            optimal_values = [s['signal_value'] for s in high_profit_signals]
            optimal_mean = np.mean(optimal_values)
            optimal_std = np.std(optimal_values)

            # Calculate optimized thresholds
            if mean_signal < 0:  # Oversold indicator
                detection_threshold = optimal_mean - optimal_std
                confirmation_threshold = optimal_mean + optimal_std * 0.5
            else:  # Overbought indicator
                detection_threshold = optimal_mean + optimal_std
                confirmation_threshold = optimal_mean - optimal_std * 0.5
        else:
            # Fallback to current thresholds
            current = self._get_current_thresholds(indicator)
            detection_threshold = current['detection_oversold']
            confirmation_threshold = current['confirmation_oversold']

        optimized_thresholds = {
            'detection_oversold': detection_threshold,
            'confirmation_oversold': confirmation_threshold,
            'detection_overbought': -detection_threshold,
            'confirmation_overbought': -confirmation_threshold
        }

        return {
            'accuracy': abs(correlation),
            'thresholds': optimized_thresholds,
            'correlation': correlation,
            'signal_stats': {
                'mean': mean_signal,
                'std': std_signal,
                'count': len(signal_values)
            }
        }

    def _calculate_optimized_thresholds(self, X: np.ndarray, y: np.ndarray,
                                      feature_importance: np.ndarray,
                                      indicator: str, method: str) -> Dict:
        """Calculate optimized thresholds based on ML analysis"""
        if len(X) == 0:
            return self._get_current_thresholds(indicator)

        # Analyze signal values (first feature)
        signal_values = X[:, 0]

        # Find optimal ranges based on profitability
        high_profit_mask = y >= 2.0  # High profit signals
        medium_profit_mask = (y >= 1.0) & (y < 2.0)  # Medium profit signals

        if np.sum(high_profit_mask) > 0:
            high_profit_values = signal_values[high_profit_mask]
            optimal_range = [np.min(high_profit_values), np.max(high_profit_values)]
        elif np.sum(medium_profit_mask) > 0:
            medium_profit_values = signal_values[medium_profit_mask]
            optimal_range = [np.min(medium_profit_values), np.max(medium_profit_values)]
        else:
            # Fallback to current thresholds
            return self._get_current_thresholds(indicator)

        # Calculate new thresholds based on optimal range
        if optimal_range[0] < 0:  # Oversold signals
            detection_threshold = optimal_range[0] * 1.1  # Slightly more sensitive
            confirmation_threshold = optimal_range[1] * 0.8  # Easier confirmation

            return {
                'detection_oversold': detection_threshold,
                'confirmation_oversold': confirmation_threshold,
                'detection_overbought': -detection_threshold,
                'confirmation_overbought': -confirmation_threshold
            }
        else:  # Overbought signals
            detection_threshold = optimal_range[1] * 1.1
            confirmation_threshold = optimal_range[0] * 0.8

            return {
                'detection_overbought': detection_threshold,
                'confirmation_overbought': confirmation_threshold,
                'detection_oversold': -detection_threshold,
                'confirmation_oversold': -confirmation_threshold
            }

    def _ensemble_optimization(self, ml_results: Dict, indicator: str) -> Dict:
        """Combine results from multiple ML methods using ensemble approach"""
        if not ml_results:
            return {'accuracy': 0, 'thresholds': {}}

        # Weight methods by their accuracy
        total_weight = 0
        weighted_thresholds = {}

        for method, result in ml_results.items():
            if method == 'ensemble':  # Skip to avoid recursion
                continue

            accuracy = result.get('accuracy', 0)
            thresholds = result.get('thresholds', {})

            if accuracy > 0 and thresholds:
                weight = accuracy ** 2  # Square to emphasize better methods
                total_weight += weight

                for key, value in thresholds.items():
                    if key not in weighted_thresholds:
                        weighted_thresholds[key] = 0
                    weighted_thresholds[key] += value * weight

        # Calculate weighted average
        if total_weight > 0:
            for key in weighted_thresholds:
                weighted_thresholds[key] /= total_weight

        # Calculate ensemble accuracy
        accuracies = [r.get('accuracy', 0) for r in ml_results.values() if 'accuracy' in r]
        ensemble_accuracy = np.mean(accuracies) if accuracies else 0

        return {
            'accuracy': ensemble_accuracy,
            'thresholds': weighted_thresholds,
            'method_weights': {method: ml_results[method].get('accuracy', 0) ** 2
                             for method in ml_results if method != 'ensemble'}
        }

    def test_optimized_combinations(self, data_1min: pd.DataFrame,
                                  higher_timeframe_data: Dict[str, pd.DataFrame],
                                  indicators: List[str]) -> Dict[str, Any]:
        """
        🔍 TEST OPTIMIZED 14 TIMEFRAME COMBINATIONS
        - Test all 14 specific combinations with optimized thresholds
        - Identify best performing combinations
        - Generate comprehensive performance metrics
        """
        print("\n🔍 TESTING OPTIMIZED 14 TIMEFRAME COMBINATIONS")
        print("================================================================================")

        combinations = [
            ['15min'],
            ['3min', '15min'],
            ['5min', '15min'],
            ['3min'],
            ['5min'],
            ['3min', '15min', '30min'],
            ['5min', '15min', '30min'],
            ['15min', '30min'],
            ['3min', '30min'],
            ['5min', '30min'],
            ['5min', '15min', '30min', '60min'],
            ['5min', '60min'],
            ['15min', '60min'],
            ['3min', '15min', '60min']
        ]

        combination_results = {}

        for i, combination in enumerate(combinations, 1):
            print(f"\n📊 Testing Combination {i}: {' + '.join(combination)}")

            # Test each indicator with this combination
            combo_performance = {}

            for indicator in indicators:
                if indicator not in self.optimized_thresholds:
                    continue

                # Generate signals with optimized thresholds
                signals = self._generate_optimized_signals(data_1min, indicator)

                # Test confirmation with this combination
                confirmed_signals = []
                for signal in signals:
                    confirmation_result = self._test_combination_confirmation(
                        signal, higher_timeframe_data, combination, indicator
                    )

                    if confirmation_result['confirmed']:
                        confirmed_signals.append({
                            **signal,
                            'confirmation_strength': confirmation_result['strength'],
                            'confirmation_details': confirmation_result['details']
                        })

                # Calculate performance metrics
                total_signals = len(signals)
                confirmed_count = len(confirmed_signals)
                confirmation_rate = confirmed_count / total_signals if total_signals > 0 else 0

                combo_performance[indicator] = {
                    'total_signals': total_signals,
                    'confirmed_signals': confirmed_count,
                    'confirmation_rate': confirmation_rate,
                    'signals': confirmed_signals
                }

            combination_results[f"combination_{i}"] = {
                'timeframes': combination,
                'performance': combo_performance,
                'overall_confirmation_rate': np.mean([
                    combo_performance[ind]['confirmation_rate']
                    for ind in combo_performance
                ]) if combo_performance else 0
            }

        return combination_results

    def generate_summary_report(self, learning_results: Dict,
                              optimization_results: Dict,
                              combination_results: Dict) -> Dict[str, Any]:
        """
        📋 GENERATE COMPREHENSIVE SUMMARY REPORT
        - Optimized threshold values for all indicators
        - Best performing timeframe combinations
        - ML learning insights and recommendations
        """
        print("\n📋 GENERATING COMPREHENSIVE SUMMARY REPORT")
        print("================================================================================")

        summary = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'optimized_thresholds': {},
            'best_combinations': {},
            'ml_insights': {},
            'recommendations': []
        }

        # Compile optimized thresholds
        for indicator in self.optimized_thresholds:
            thresholds = self.optimized_thresholds[indicator]
            summary['optimized_thresholds'][indicator] = {
                'detection_oversold': round(thresholds.get('detection_oversold', 0), 2),
                'confirmation_oversold': round(thresholds.get('confirmation_oversold', 0), 2),
                'detection_overbought': round(thresholds.get('detection_overbought', 0), 2),
                'confirmation_overbought': round(thresholds.get('confirmation_overbought', 0), 2)
            }

        # Identify best combinations
        best_combinations = []
        for combo_key, combo_data in combination_results.items():
            overall_rate = combo_data['overall_confirmation_rate']
            if overall_rate > 0.7:  # 70% threshold for "best"
                best_combinations.append({
                    'combination': combo_data['timeframes'],
                    'confirmation_rate': round(overall_rate, 3),
                    'combo_id': combo_key
                })

        # Sort by confirmation rate
        best_combinations.sort(key=lambda x: x['confirmation_rate'], reverse=True)
        summary['best_combinations'] = best_combinations[:5]  # Top 5

        # ML insights
        for indicator, opt_result in optimization_results.items():
            if 'ensemble' in opt_result:
                ensemble_data = opt_result['ensemble']
                summary['ml_insights'][indicator] = {
                    'best_accuracy': round(ensemble_data.get('accuracy', 0), 3),
                    'method_weights': ensemble_data.get('method_weights', {}),
                    'optimization_success': ensemble_data.get('accuracy', 0) > 0.5
                }

        # Generate recommendations
        recommendations = []

        # Threshold recommendations
        for indicator in summary['optimized_thresholds']:
            recommendations.append(
                f"Use optimized {indicator} thresholds: "
                f"Detection {summary['optimized_thresholds'][indicator]['detection_oversold']:.2f} → "
                f"Confirmation {summary['optimized_thresholds'][indicator]['confirmation_oversold']:.2f}"
            )

        # Combination recommendations
        if best_combinations:
            top_combo = best_combinations[0]
            recommendations.append(
                f"Best timeframe combination: {' + '.join(top_combo['combination'])} "
                f"(Confirmation rate: {top_combo['confirmation_rate']:.1%})"
            )

        summary['recommendations'] = recommendations

        return summary

    def _generate_optimized_signals(self, data: pd.DataFrame, indicator: str) -> List[Dict]:
        """Generate signals using optimized thresholds"""
        if indicator not in self.optimized_thresholds:
            return self._detect_1min_signals(data, indicator)

        signals = []
        optimized = self.optimized_thresholds[indicator]

        for i in range(1, len(data)):
            if indicator not in data.columns:
                continue

            value = data[indicator].iloc[i]
            prev_value = data[indicator].iloc[i-1]

            # BUY signal with optimized thresholds
            if (prev_value <= optimized.get('detection_oversold', -2.0) and
                value >= optimized.get('confirmation_oversold', -1.0)):
                signals.append({
                    'time': data.index[i],
                    'type': 'BUY',
                    'value': value,
                    'detection_value': prev_value,
                    'price': data['close'].iloc[i] if 'close' in data.columns else None
                })

            # SELL signal with optimized thresholds
            if (prev_value >= optimized.get('detection_overbought', 2.0) and
                value <= optimized.get('confirmation_overbought', 1.0)):
                signals.append({
                    'time': data.index[i],
                    'type': 'SELL',
                    'value': value,
                    'detection_value': prev_value,
                    'price': data['close'].iloc[i] if 'close' in data.columns else None
                })

        return signals

    def _test_combination_confirmation(self, signal: Dict, htf_data: Dict[str, pd.DataFrame],
                                     combination: List[str], indicator: str) -> Dict:
        """Test if signal is confirmed by higher timeframe combination"""
        signal_time = signal['time']
        signal_type = signal['type']

        confirmations = []

        for timeframe in combination:
            if timeframe not in htf_data or indicator not in htf_data[timeframe].columns:
                continue

            # Get HTF value at signal time
            htf_df = htf_data[timeframe]
            closest_idx = htf_df.index.get_indexer([signal_time], method='nearest')[0]

            if closest_idx >= 0 and closest_idx < len(htf_df):
                htf_value = htf_df[indicator].iloc[closest_idx]

                # Check confirmation based on optimized HTF thresholds
                htf_thresholds = self._get_htf_optimized_thresholds(indicator, timeframe)

                if signal_type == 'BUY':
                    if htf_value <= htf_thresholds.get('confirmation_oversold', -1.0):
                        confirmations.append({
                            'timeframe': timeframe,
                            'value': htf_value,
                            'threshold': htf_thresholds['confirmation_oversold'],
                            'confirmed': True
                        })
                elif signal_type == 'SELL':
                    if htf_value >= htf_thresholds.get('confirmation_overbought', 1.0):
                        confirmations.append({
                            'timeframe': timeframe,
                            'value': htf_value,
                            'threshold': htf_thresholds['confirmation_overbought'],
                            'confirmed': True
                        })

        # Calculate confirmation strength
        confirmed_count = len(confirmations)
        total_timeframes = len(combination)
        confirmation_strength = confirmed_count / total_timeframes if total_timeframes > 0 else 0

        return {
            'confirmed': confirmation_strength >= 0.5,  # At least 50% of timeframes confirm
            'strength': confirmation_strength,
            'details': confirmations
        }

    def _get_htf_optimized_thresholds(self, indicator: str, timeframe: str) -> Dict:
        """Get optimized higher timeframe thresholds"""
        # Base optimized thresholds
        base_thresholds = self.optimized_thresholds.get(indicator, self._get_current_thresholds(indicator))

        # Scale for higher timeframes (smaller values for higher timeframes)
        timeframe_multipliers = {
            '3min': 0.9,
            '5min': 0.8,
            '15min': 0.7,
            '30min': 0.6,
            '60min': 0.5
        }

        multiplier = timeframe_multipliers.get(timeframe, 1.0)

        return {
            'detection_oversold': base_thresholds.get('detection_oversold', -2.0) * multiplier,
            'confirmation_oversold': base_thresholds.get('confirmation_oversold', -1.0) * multiplier,
            'detection_overbought': base_thresholds.get('detection_overbought', 2.0) * multiplier,
            'confirmation_overbought': base_thresholds.get('confirmation_overbought', 1.0) * multiplier
        }

    def run_complete_ml_learning_system(self, data_1min: pd.DataFrame,
                                      higher_timeframe_data: Dict[str, pd.DataFrame],
                                      indicators: List[str],
                                      output_file: str = None) -> Dict[str, Any]:
        """
        🚀 RUN COMPLETE ML LEARNING SYSTEM
        - Identify true signals
        - Optimize thresholds with multiple ML methods
        - Test all 14 combinations
        - Generate comprehensive summary
        """
        print("\n🚀 RUNNING COMPLETE ML LEARNING SYSTEM")
        print("================================================================================")
        print("🎯 Phase 1: True Signal Identification")
        print("🤖 Phase 2: Advanced ML Threshold Optimization")
        print("🔍 Phase 3: Testing 14 Timeframe Combinations")
        print("📋 Phase 4: Comprehensive Summary Generation")
        print("================================================================================")

        # Phase 1: Identify true signals
        learning_results = self.identify_true_signals_with_validation(
            data_1min, higher_timeframe_data, indicators
        )

        print(f"\n✅ Phase 1 Complete: {len(learning_results['true_signals'])} true signals identified")
        print(f"📊 True signal rate: {learning_results['true_signal_rate']:.1%}")

        # Phase 2: ML optimization (multiple rounds)
        optimization_results = self.advanced_ml_threshold_optimization(
            learning_results['true_signals'], indicators, optimization_rounds=3
        )

        print(f"\n✅ Phase 2 Complete: Thresholds optimized for {len(optimization_results)} indicators")

        # Phase 3: Test combinations
        combination_results = self.test_optimized_combinations(
            data_1min, higher_timeframe_data, indicators
        )

        print(f"\n✅ Phase 3 Complete: {len(combination_results)} combinations tested")

        # Phase 4: Generate summary
        summary = self.generate_summary_report(
            learning_results, optimization_results, combination_results
        )

        print(f"\n✅ Phase 4 Complete: Comprehensive summary generated")

        # Save to Excel if output file specified
        if output_file:
            self._save_ml_results_to_excel(
                learning_results, optimization_results, combination_results, summary, output_file
            )
            print(f"\n💾 Results saved to: {output_file}")

        return {
            'learning_results': learning_results,
            'optimization_results': optimization_results,
            'combination_results': combination_results,
            'summary': summary
        }

    def _save_ml_results_to_excel(self, learning_results: Dict, optimization_results: Dict,
                                combination_results: Dict, summary: Dict, output_file: str):
        """Save comprehensive ML results to Excel"""
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:

                # Summary Sheet
                summary_data = []
                for indicator, thresholds in summary['optimized_thresholds'].items():
                    summary_data.append({
                        'Indicator': indicator,
                        'Detection_Oversold': thresholds['detection_oversold'],
                        'Confirmation_Oversold': thresholds['confirmation_oversold'],
                        'Detection_Overbought': thresholds['detection_overbought'],
                        'Confirmation_Overbought': thresholds['confirmation_overbought']
                    })

                pd.DataFrame(summary_data).to_excel(writer, sheet_name='Optimized_Thresholds', index=False)

                # Best Combinations Sheet
                if summary['best_combinations']:
                    combo_data = []
                    for combo in summary['best_combinations']:
                        combo_data.append({
                            'Combination': ' + '.join(combo['combination']),
                            'Confirmation_Rate': combo['confirmation_rate'],
                            'Combo_ID': combo['combo_id']
                        })
                    pd.DataFrame(combo_data).to_excel(writer, sheet_name='Best_Combinations', index=False)

                # True Signals Sheet
                if learning_results['true_signals']:
                    true_signals_data = []
                    for signal in learning_results['true_signals']:
                        true_signals_data.append({
                            'Indicator': signal['indicator'],
                            'Signal_Time': signal['signal_time'],
                            'Signal_Type': signal['signal_type'],
                            'Signal_Value': signal['signal_value'],
                            'Profit_Achieved': signal['profit_achieved'],
                            'Time_to_Profit': signal['time_to_profit']
                        })
                    pd.DataFrame(true_signals_data).to_excel(writer, sheet_name='True_Signals', index=False)

                # ML Insights Sheet
                ml_insights_data = []
                for indicator, insights in summary['ml_insights'].items():
                    ml_insights_data.append({
                        'Indicator': indicator,
                        'Best_Accuracy': insights['best_accuracy'],
                        'Optimization_Success': insights['optimization_success']
                    })
                pd.DataFrame(ml_insights_data).to_excel(writer, sheet_name='ML_Insights', index=False)

                # Recommendations Sheet
                recommendations_data = [{'Recommendation': rec} for rec in summary['recommendations']]
                pd.DataFrame(recommendations_data).to_excel(writer, sheet_name='Recommendations', index=False)

                print(f"✅ ML results successfully saved to {output_file}")

        except Exception as e:
            print(f"❌ Error saving ML results: {str(e)}")

    def _check_breakout_signal(self, indicator: str, value: float, timeframe: str) -> Optional[Dict[str, Any]]:
        """Check for breakout signals using ML-optimized thresholds"""
        
        if indicator not in self.ml_optimized_thresholds:
            return None
        
        thresholds = self.ml_optimized_thresholds[indicator]['breakout']
        
        # Oversold breakout (BUY signal)
        if value <= thresholds['oversold_breakout']:
            return {
                'signal_type': 'BUY',
                'reason': f'BREAKOUT_OVERSOLD (≤{thresholds["oversold_breakout"]})',
                'strength': min(abs(value / thresholds['oversold_breakout']), 2.0)
            }
        
        # Overbought breakout (SELL signal)
        if value >= thresholds['overbought_breakout']:
            return {
                'signal_type': 'SELL',
                'reason': f'BREAKOUT_OVERBOUGHT (≥{thresholds["overbought_breakout"]})',
                'strength': min(abs(value / thresholds['overbought_breakout']), 2.0)
            }
        
        return None


def main():
    """Demo the advanced ML-enhanced analyzer"""
    
    print("🚀 ADVANCED ML-ENHANCED PROFESSIONAL ANALYZER DEMO")
    print("=" * 80)
    print("⏰ Real time format (not Period_1, Period_2)")
    print("🎯 Proper stop loss and targets for PGO")
    print("🤖 ML-based threshold optimization for near 1.0 accuracy")
    print("🔄 Separate thresholds for reversals vs breakouts")
    
    analyzer = AdvancedMLEnhancedProfessionalAnalyzer()
    
    # This would integrate with the existing multi-interval analyzer
    print("\n💡 Integration with enhanced_multi_interval_professional_analyzer.py")
    print("💡 Use this analyzer in the advanced analysis phase")


if __name__ == "__main__":
    main()
